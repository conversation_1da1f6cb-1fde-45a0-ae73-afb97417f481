import 'package:flutter/material.dart';

/// 主题颜色配置
/// 
/// 通过修改这个文件中的种子颜色，可以快速调整整个app的主题配色方案
/// 基于Material Design 3的ColorScheme.fromSeed()自动生成和谐的配色
class ThemeColorConfig {
  ThemeColorConfig._();

  // ========== 浅色主题配置 ==========
  
  /// 浅色主题种子颜色
  /// 
  /// 推荐颜色方案：
  /// - Material Blue 500: 0xFF2196F3 (当前使用)
  /// - Material Indigo 500: 0xFF3F51B5
  /// - Material Teal 500: 0xFF009688
  /// - Material Green 500: 0xFF4CAF50
  /// - Material Purple 500: 0xFF9C27B0
  /// - 自定义蓝色: 0xFF1976D2
  static const Color lightThemeSeed = Color(0xFF2196F3);
  
  /// 浅色主题名称
  static const String lightThemeName = '经典浅色';
  
  /// 浅色主题描述
  static const String lightThemeDescription = '清晰明亮，适合白天使用，基于Material Blue的专业配色';

  // ========== 深色主题配置 ==========
  
  /// 深色主题种子颜色
  /// 
  /// 推荐颜色方案：
  /// - Material Purple 200: 0xFFBB86FC (当前使用)
  /// - Material Blue 200: 0xFF90CAF9
  /// - Material Teal 200: 0xFF80CBC4
  /// - Material Green 200: 0xFFA5D6A7
  /// - Material Amber 200: 0xFFFFE082
  /// - 自定义紫色: 0xFFAB47BC
  static const Color darkThemeSeed = Color(0xFF90CAF9);
  
  /// 深色主题名称
  static const String darkThemeName = '经典深色';
  
  /// 深色主题描述
  static const String darkThemeDescription = '护眼舒适，适合夜间使用，基于Material Purple的温和配色';

  // ========== 预设配色方案 ==========
  
  /// 预设配色方案
  static const List<ThemeColorPreset> presets = [
    // 经典蓝色方案
    ThemeColorPreset(
      name: '经典蓝色',
      description: '专业可靠的蓝色系配色',
      lightSeed: Color(0xFF2196F3),
      darkSeed: Color(0xFFBB86FC),
    ),
    
    // 自然绿色方案
    ThemeColorPreset(
      name: '自然绿色',
      description: '贴近自然的绿色系配色',
      lightSeed: Color(0xFF4CAF50),
      darkSeed: Color(0xFFA5D6A7),
    ),
    
    // 温暖橙色方案
    ThemeColorPreset(
      name: '温暖橙色',
      description: '活力温暖的橙色系配色',
      lightSeed: Color(0xFFFF9800),
      darkSeed: Color(0xFFFFE082),
    ),
    
    // 优雅紫色方案
    ThemeColorPreset(
      name: '优雅紫色',
      description: '优雅神秘的紫色系配色',
      lightSeed: Color(0xFF9C27B0),
      darkSeed: Color(0xFFCE93D8),
    ),
    
    // 深海蓝色方案
    ThemeColorPreset(
      name: '深海蓝色',
      description: '深邃专业的深蓝配色',
      lightSeed: Color(0xFF1976D2),
      darkSeed: Color(0xFF90CAF9),
    ),
    
    // 森林绿色方案
    ThemeColorPreset(
      name: '森林绿色',
      description: '沉稳自然的深绿配色',
      lightSeed: Color(0xFF388E3C),
      darkSeed: Color(0xFF80CBC4),
    ),
  ];

  // ========== 辅助方法 ==========
  
  /// 获取当前浅色主题的ColorScheme
  static ColorScheme getLightColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: lightThemeSeed,
      brightness: Brightness.light,
    );
  }
  
  /// 获取当前深色主题的ColorScheme
  static ColorScheme getDarkColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: darkThemeSeed,
      brightness: Brightness.dark,
    );
  }
  
  /// 根据预设方案获取浅色ColorScheme
  static ColorScheme getLightColorSchemeFromPreset(ThemeColorPreset preset) {
    return ColorScheme.fromSeed(
      seedColor: preset.lightSeed,
      brightness: Brightness.light,
    );
  }
  
  /// 根据预设方案获取深色ColorScheme
  static ColorScheme getDarkColorSchemeFromPreset(ThemeColorPreset preset) {
    return ColorScheme.fromSeed(
      seedColor: preset.darkSeed,
      brightness: Brightness.dark,
    );
  }
  
  /// 获取颜色的十六进制字符串表示
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
  }
  
  /// 从十六进制字符串创建颜色
  static Color colorFromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  // ========== 调试和预览方法 ==========
  
  /// 获取当前配置的调试信息
  static Map<String, dynamic> getDebugInfo() {
    return {
      'lightTheme': {
        'name': lightThemeName,
        'seedColor': colorToHex(lightThemeSeed),
        'description': lightThemeDescription,
      },
      'darkTheme': {
        'name': darkThemeName,
        'seedColor': colorToHex(darkThemeSeed),
        'description': darkThemeDescription,
      },
      'presets': presets.map((preset) => {
        'name': preset.name,
        'lightSeed': colorToHex(preset.lightSeed),
        'darkSeed': colorToHex(preset.darkSeed),
        'description': preset.description,
      }).toList(),
    };
  }
  
  /// 打印当前配置信息
  static void printCurrentConfig() {
    debugPrint('=== 当前主题配置 ===');
    debugPrint('浅色主题: $lightThemeName (${colorToHex(lightThemeSeed)})');
    debugPrint('深色主题: $darkThemeName (${colorToHex(darkThemeSeed)})');
    debugPrint('可用预设: ${presets.length}个');
  }
}

/// 主题颜色预设
class ThemeColorPreset {
  /// 预设名称
  final String name;
  
  /// 预设描述
  final String description;
  
  /// 浅色主题种子颜色
  final Color lightSeed;
  
  /// 深色主题种子颜色
  final Color darkSeed;
  
  const ThemeColorPreset({
    required this.name,
    required this.description,
    required this.lightSeed,
    required this.darkSeed,
  });
  
  /// 获取浅色ColorScheme
  ColorScheme getLightColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: lightSeed,
      brightness: Brightness.light,
    );
  }
  
  /// 获取深色ColorScheme
  ColorScheme getDarkColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: darkSeed,
      brightness: Brightness.dark,
    );
  }
  
  @override
  String toString() {
    return 'ThemeColorPreset(name: $name, light: ${ThemeColorConfig.colorToHex(lightSeed)}, dark: ${ThemeColorConfig.colorToHex(darkSeed)})';
  }
}