import 'package:flutter/foundation.dart';

/// 应用配置类，用于管理开发和生产环境的配置
class AppConfig {
  // 私有构造函数，确保单例模式
  AppConfig._();

  static final AppConfig _instance = AppConfig._();
  static AppConfig get instance => _instance;

  /// PocketBase 服务器配置
  String get pocketBaseUrl {
    // 可以通过环境变量配置不同环境的服务器地址
    const envUrl = String.fromEnvironment('POCKETBASE_URL');
    if (envUrl.isNotEmpty) {
      return envUrl;
    }

    // 默认开发服务器地址（需要根据实际情况修改）
    return 'http://117.72.60.131:8090';
  }

  /// 是否为开发模式
  /// 在debug模式下默认为true，release模式下默认为false
  /// 可以通过环境变量 FLUTTER_DEV_MODE 来覆盖
  bool get isDevelopmentMode {
    // 检查环境变量
    const devModeEnv = String.fromEnvironment('FLUTTER_DEV_MODE');
    if (devModeEnv.isNotEmpty) {
      return devModeEnv.toLowerCase() == 'true';
    }

    // 默认在debug模式下启用开发功能
    return kDebugMode;
  }

  /// 是否启用开发者工具
  bool get enableDeveloperTools => isDevelopmentMode;

  /// 是否显示调试信息
  bool get showDebugInfo => isDevelopmentMode;

  /// 是否启用PocketBase测试页面
  bool get enablePocketBaseTestPage => isDevelopmentMode;

  /// 是否启用性能监控
  bool get enablePerformanceMonitoring => isDevelopmentMode;

  /// 是否启用网络日志
  bool get enableNetworkLogging => isDevelopmentMode;

  /// 是否启用缓存调试
  bool get enableCacheDebugging => isDevelopmentMode;

  /// 获取应用版本信息
  String get appVersion => '1.0.0';

  /// 获取构建模式
  String get buildMode {
    if (kDebugMode) return 'Debug';
    if (kProfileMode) return 'Profile';
    return 'Release';
  }

  /// 获取平台信息
  String get platformInfo {
    if (kIsWeb) return 'Web';
    return defaultTargetPlatform.name;
  }

  /// 地图相关配置
  /// 地图标记大小（宽度和高度）
  double get mapMarkerSize => 80.0;

  /// 开发者菜单配置
  Map<String, dynamic> get developerMenuConfig => {
    'showPocketBaseTest': enablePocketBaseTestPage,
    'showNetworkLogs': enableNetworkLogging,
    'showCacheStats': enableCacheDebugging,
    'showPerformanceStats': enablePerformanceMonitoring,
  };

  /// 打印配置信息（仅在开发模式下）
  void printConfigInfo() {
    if (!isDevelopmentMode) return;

    debugPrint('=== App Configuration ===');
    debugPrint('Development Mode: $isDevelopmentMode');
    debugPrint('Build Mode: $buildMode');
    debugPrint('Platform: $platformInfo');
    debugPrint('App Version: $appVersion');
    debugPrint('Developer Tools: $enableDeveloperTools');
    debugPrint('PocketBase Test Page: $enablePocketBaseTestPage');
    debugPrint('========================');
  }
}
