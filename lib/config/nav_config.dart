import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 导航项配置
class NavItemConfig {
  final IconData icon;
  final String label;
  final List<Color> gradientColors;
  final double unselectedIconSize;
  final double selectedIconSize;

  const NavItemConfig({
    required this.icon,
    required this.label,
    required this.gradientColors,
    this.unselectedIconSize = 26.0,
    this.selectedIconSize = 20.0,
  });
}

/// [*参数设置*]导航配置管理
class NavConfig {
  NavConfig._();

  /// 导航项配置映射
  static const Map<int, NavItemConfig> items = {
    0: NavItemConfig(
      icon: FontAwesomeIcons.mapLocationDot,
      label: '地图',
      gradientColors: [
        Color(0xFF2196F3), // 蓝色
        Color(0xFF00BCD4), // 青色
      ],
      unselectedIconSize: 30.0, // 地图图标稍大一些
      selectedIconSize: 22.0,
    ),
    1: NavItemConfig(
      icon: FontAwesomeIcons.route,
      label: '推荐',
      gradientColors: [
        Color(0xFFFF5722), // 红色
        Color(0xFFFF9800), // 橙色
      ],
      unselectedIconSize: 30.0, // 标准大小
      selectedIconSize: 22.0,
    ),
    2: NavItemConfig(
      icon: FontAwesomeIcons.comment,
      label: '信息',
      gradientColors: [
        Color(0xFF9C27B0), // 紫色
        Color(0xFFE91E63), // 粉色
      ],
      unselectedIconSize: 30.0, // 标准大小
      selectedIconSize: 22.0,
    ),
    3: NavItemConfig(
      icon: FontAwesomeIcons.user,
      label: '我的',
      gradientColors: [
        Color.fromARGB(255, 35, 235, 0), // 橙色
        Color.fromARGB(255, 1, 211, 234), // 红色
      ],
      unselectedIconSize: 30.0, // 用户图标稍小一些
      selectedIconSize: 22.0,
    ),
  };

  /// 获取导航项配置
  static NavItemConfig? getConfig(int index) {
    return items[index];
  }

  /// 获取所有导航项配置
  static List<NavItemConfig> getAllConfigs() {
    return items.values.toList();
  }
}
