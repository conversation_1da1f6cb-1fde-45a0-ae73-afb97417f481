import 'package:latlong2/latlong.dart';
import 'fishing_spot.dart';

/// 钓鱼活动模型类
///
/// 专门用于"一起钓鱼"约钓活动
/// 适配 PocketBase fishing_activities 集合结构
class FishingActivity {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 活动标题 (必填，对应数据库 title 字段)
  final String title;

  /// 描述信息 (可选)
  String description;

  /// 活动位置 (geoPoint字段)
  final Map<String, dynamic>? location;

  /// 开始时间 (对应数据库 start_time 字段)
  final DateTime startTime;

  /// 持续时长（小时，对应数据库 duration 字段）
  final double duration;

  /// 最大参与人数
  final int maxParticipants;

  /// 当前参与人数
  final int currentParticipants;

  /// 创建者用户ID (对应数据库 creator_id 字段)
  final String creatorId;

  /// 创建者用户名 (从关联的用户数据获取)
  final String? creatorName;

  /// 状态 (active, cancelled, completed)
  String status;

  /// 图片信息 (JSON格式)
  final Map<String, dynamic>? images;

  /// 群聊ID (可选，暂时不使用)
  final String? groupChatId;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  FishingActivity({
    required this.id,
    required this.title,
    this.description = '',
    this.location,
    required this.startTime,
    this.duration = 2.0,
    this.maxParticipants = 10,
    this.currentParticipants = 1,
    required this.creatorId,
    this.creatorName,
    this.status = 'active',
    this.images,
    this.groupChatId,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建钓鱼活动对象
  factory FishingActivity.fromJson(Map<String, dynamic> json) {
    // 处理location字段（geoPoint类型）
    Map<String, dynamic>? locationData;
    if (json['location'] != null) {
      locationData =
          json['location'] is Map<String, dynamic> ? json['location'] : null;
    } else if (json['latitude'] != null && json['longitude'] != null) {
      // 向后兼容：如果有旧的latitude/longitude字段，转换为location格式
      locationData = {
        'lat': (json['latitude'] as num).toDouble(),
        'lon': (json['longitude'] as num).toDouble(),
      };
    }

    return FishingActivity(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: locationData,
      startTime:
          json['start_time'] != null
              ? DateTime.parse(json['start_time'])
              : DateTime.now(),
      duration: (json['duration'] as num?)?.toDouble() ?? 2.0,
      maxParticipants: json['max_participants'] ?? 10,
      currentParticipants: json['current_participants'] ?? 1,
      creatorId: json['creator_id'] ?? '',
      creatorName: json['creator_name'],
      status: json['status'] ?? 'active',
      images:
          json['images'] is Map<String, dynamic>
              ? json['images'] as Map<String, dynamic>
              : null,
      groupChatId: json['group_chat_id'],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'start_time': startTime.toIso8601String(),
      'duration': duration,
      'max_participants': maxParticipants,
      'current_participants': currentParticipants,
      'creator_id': creatorId,
      'creator_name': creatorName,
      'status': status,
      'images': images,
      'group_chat_id': groupChatId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  /// 获取位置的LatLng对象
  LatLng get locationLatLng {
    if (location == null) {
      throw Exception('活动位置信息不完整');
    }
    return LatLng(
      (location!['lat'] as num).toDouble(),
      (location!['lon'] as num).toDouble(),
    );
  }

  /// 获取活动结束时间
  DateTime get endTime {
    return startTime.add(
      Duration(hours: duration.toInt(), minutes: ((duration % 1) * 60).toInt()),
    );
  }

  /// 检查活动是否已过期
  bool get isExpired {
    return DateTime.now().isAfter(endTime);
  }

  /// 检查活动是否即将开始（1小时内）
  bool get isStartingSoon {
    final now = DateTime.now();
    final oneHourBefore = startTime.subtract(const Duration(hours: 1));
    return now.isAfter(oneHourBefore) && now.isBefore(startTime);
  }

  /// 检查活动是否正在进行中
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// 获取活动状态描述
  String get statusDescription {
    if (isExpired) return '已结束';
    if (isOngoing) return '进行中';
    if (isStartingSoon) return '即将开始';
    return '等待中';
  }

  /// 转换为钓点对象（用于复用钓点标记显示）
  FishingSpot toFishingSpot() {
    return FishingSpot(
      id: id,
      name: title,
      description: description,
      location: location,
      userId: creatorId,
      userName: creatorName,
      spotEmoji: '🎣', // 约钓活动专用emoji
      fishEmoji: '🐟',
      status: status,
      created: created,
      updated: updated,
    );
  }
}
