import 'package:uuid/uuid.dart';

/// 用户模型类
///
/// 遵循 PocketBase 标准字段命名规范：
/// - 使用 PocketBase 内置的 id, created, updated 字段
/// - 自定义字段使用下划线命名法
/// - 关系字段通过单独的集合管理，避免 JSON 数组
class User {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 用户名 (必填，唯一)
  final String username;

  /// 邮箱 (必填，唯一，用于认证)
  final String email;

  /// 手机号 (可选)
  String? phone;

  /// 头像URL (可选)
  String? avatar;

  /// 个人简介 (可选)
  String? bio;

  /// 积分 (默认0)
  int points;

  /// 是否已验证邮箱
  bool emailVisibility;

  /// 是否已验证
  bool verified;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  /// 最后登录时间 (应用层维护)
  DateTime? lastLoginAt;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.phone,
    this.avatar,
    this.bio,
    this.points = 0,
    this.emailVisibility = false,
    this.verified = false,
    required this.created,
    required this.updated,
    this.lastLoginAt,
  });

  /// 从JSON创建用户对象
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      avatar: json['avatar'],
      bio: json['bio'],
      points: json['points'] ?? 0,
      emailVisibility: json['emailVisibility'] ?? false,
      verified: json['verified'] ?? false,
      created:
          json['created'] != null
              ? DateTime.parse(json['created'])
              : DateTime.now(),
      updated:
          json['updated'] != null
              ? DateTime.parse(json['updated'])
              : DateTime.now(),
      lastLoginAt:
          json['lastLoginAt'] != null
              ? DateTime.parse(json['lastLoginAt'])
              : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'bio': bio,
      'points': points,
      'emailVisibility': emailVisibility,
      'verified': verified,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  /// 创建用户副本，可选择性地更新某些字段
  User copyWith({
    String? id,
    String? username,
    String? email,
    String? phone,
    String? avatar,
    String? bio,
    int? points,
    bool? emailVisibility,
    bool? verified,
    DateTime? created,
    DateTime? updated,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      points: points ?? this.points,
      emailVisibility: emailVisibility ?? this.emailVisibility,
      verified: verified ?? this.verified,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  /// 便捷方法：获取昵称（兼容旧代码）
  String get nickname => username;

  /// 便捷方法：获取头像URL（兼容旧代码）
  String get avatarUrl => avatar ?? '';

  /// 便捷方法：获取手机号（兼容旧代码）
  String get phoneNumber => phone ?? '';

  /// 便捷方法：获取创建时间（兼容旧代码）
  DateTime get createdAt => created;

  /// 便捷方法：获取更新时间（兼容旧代码）
  DateTime get updatedAt => updated;

  /// 便捷方法：获取发布的钓点列表（兼容旧代码）
  /// 实际应该通过关联查询 fishing_spots 集合获取
  List<String> get publishedSpots => [];

  /// 便捷方法：获取收藏的钓点列表（兼容旧代码）
  /// 实际应该通过关联查询 user_favorites 集合获取
  List<String> get favoriteSpots => [];

  /// 便捷方法：获取关注的用户列表（兼容旧代码）
  /// 实际应该通过关联查询 user_follows 集合获取
  List<String> get following => [];

  /// 便捷方法：获取粉丝列表（兼容旧代码）
  /// 实际应该通过关联查询 user_follows 集合获取
  List<String> get followers => [];
}

/// 消息模型类
class Message {
  /// 唯一标识符
  final String id;

  /// 发送者ID
  final String senderId;

  /// 接收者ID
  final String receiverId;

  /// 消息内容
  final String content;

  /// 发送时间
  final DateTime sentAt;

  /// 是否已读
  bool isRead;

  Message({
    String? id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    DateTime? sentAt,
    this.isRead = false,
  }) : id = id ?? const Uuid().v4(),
       sentAt = sentAt ?? DateTime.now();

  /// 从JSON创建消息对象
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['senderId'],
      receiverId: json['receiverId'],
      content: json['content'],
      sentAt: DateTime.parse(json['sentAt']),
      isRead: json['isRead'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'content': content,
      'sentAt': sentAt.toIso8601String(),
      'isRead': isRead,
    };
  }
}
