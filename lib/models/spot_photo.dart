/// 钓点照片模型
/// 
/// 用于管理钓点相关的照片
/// 遵循 PocketBase 关系型数据库设计原则
class SpotPhoto {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 钓点ID
  final String spotId;

  /// 上传者用户ID
  final String userId;

  /// 照片文件名
  final String filename;

  /// 照片URL
  final String url;

  /// 缩略图URL
  final String? thumbnailUrl;

  /// R2存储路径
  final String? storagePath;

  /// 缩略图存储路径
  final String? thumbnailPath;

  /// 照片类型 (normal, panorama)
  final String type;

  /// 照片描述 (可选)
  String? description;

  /// 排序顺序
  int sortOrder;

  /// 文件大小（字节）
  final int? fileSize;

  /// MIME类型
  final String? mimeType;

  /// 是否相机拍摄
  final bool isCameraShot;

  /// 照片来源 ('camera' 或 'gallery')
  final String photoSource;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  SpotPhoto({
    required this.id,
    required this.spotId,
    required this.userId,
    required this.filename,
    required this.url,
    this.thumbnailUrl,
    this.storagePath,
    this.thumbnailPath,
    this.type = 'normal',
    this.description,
    this.sortOrder = 0,
    this.fileSize,
    this.mimeType,
    this.isCameraShot = false,
    this.photoSource = 'gallery',
    required this.created,
    required this.updated,
  });

  /// 从JSON创建照片对象
  factory SpotPhoto.fromJson(Map<String, dynamic> json) {
    return SpotPhoto(
      id: json['id'] ?? '',
      spotId: json['spot_id'] ?? '',
      userId: json['user_id'] ?? '',
      filename: json['filename'] ?? '',
      url: json['url'] ?? '',
      thumbnailUrl: json['thumbnail_url'],
      storagePath: json['storage_path'],
      thumbnailPath: json['thumbnail_path'],
      type: json['type'] ?? 'normal',
      description: json['description'],
      sortOrder: json['sort_order'] ?? 0,
      fileSize: json['file_size'],
      mimeType: json['mime_type'],
      isCameraShot: json['is_camera_shot'] ?? false,
      photoSource: json['photo_source'] ?? 'gallery',
      created: DateTime.parse(json['created'] ?? DateTime.now().toIso8601String()),
      updated: DateTime.parse(json['updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spot_id': spotId,
      'user_id': userId,
      'filename': filename,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'storage_path': storagePath,
      'thumbnail_path': thumbnailPath,
      'type': type,
      'description': description,
      'sort_order': sortOrder,
      'file_size': fileSize,
      'mime_type': mimeType,
      'is_camera_shot': isCameraShot,
      'photo_source': photoSource,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotPhoto &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SpotPhoto{id: $id, spotId: $spotId, filename: $filename, type: $type}';
  }
}
