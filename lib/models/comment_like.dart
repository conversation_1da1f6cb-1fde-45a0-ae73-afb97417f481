/// 评论点赞模型
/// 
/// 用于管理用户对评论的点赞关系
/// 遵循 PocketBase 关系型数据库设计原则
class CommentLike {
  /// 唯一标识符 (PocketBase 自动生成)
  final String id;

  /// 评论ID
  final String commentId;

  /// 用户ID
  final String userId;

  /// 创建时间 (PocketBase 自动管理)
  final DateTime created;

  /// 更新时间 (PocketBase 自动管理)
  final DateTime updated;

  CommentLike({
    required this.id,
    required this.commentId,
    required this.userId,
    required this.created,
    required this.updated,
  });

  /// 从JSON创建点赞对象
  factory CommentLike.fromJson(Map<String, dynamic> json) {
    return CommentLike(
      id: json['id'] ?? '',
      commentId: json['comment_id'] ?? '',
      userId: json['user_id'] ?? '',
      created: DateTime.parse(json['created'] ?? DateTime.now().toIso8601String()),
      updated: DateTime.parse(json['updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'comment_id': commentId,
      'user_id': userId,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommentLike &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommentLike{id: $id, commentId: $commentId, userId: $userId}';
  }
}
