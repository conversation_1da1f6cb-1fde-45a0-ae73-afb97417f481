/// 消息模型
class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final String messageType; // text, image, system
  final String status; // pending, sent, delivered, read
  final bool isRead;
  final DateTime createdAt;
  final String? senderName;
  final String? senderAvatar;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    this.messageType = 'text',
    this.status = 'sent',
    this.isRead = false,
    required this.createdAt,
    this.senderName,
    this.senderAvatar,
  });

  /// 是否是当前用户发送的消息
  bool isSentByUser(String userId) {
    return senderId == userId;
  }

  /// 获取显示的消息内容
  String get displayContent {
    switch (messageType) {
      case 'image':
        return '[图片]';
      case 'system':
        return content;
      default:
        return content;
    }
  }

  /// 获取消息状态图标
  String get statusIcon {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'sent':
        return '✓';
      case 'delivered':
        return '✓✓';
      case 'read':
        return '✓✓';
      default:
        return '';
    }
  }

  /// 复制消息并修改某些字段
  Message copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? content,
    String? messageType,
    String? status,
    bool? isRead,
    DateTime? createdAt,
    String? senderName,
    String? senderAvatar,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      messageType: messageType ?? this.messageType,
      status: status ?? this.status,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
    );
  }

  /// 从JSON创建Message对象
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] ?? '',
      senderId: json['sender_id'] ?? json['senderId'] ?? '',
      receiverId: json['receiver_id'] ?? json['receiverId'] ?? '',
      content: json['content'] ?? '',
      messageType: json['message_type'] ?? json['messageType'] ?? 'text',
      status: json['status'] ?? 'sent',
      isRead: json['is_read'] ?? json['isRead'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      senderName: json['sender_name'] ?? json['senderName'],
      senderAvatar: json['sender_avatar'] ?? json['senderAvatar'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'content': content,
      'message_type': messageType,
      'status': status,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'sender_name': senderName,
      'sender_avatar': senderAvatar,
    };
  }

  @override
  String toString() {
    return 'Message(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content, messageType: $messageType, status: $status, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
