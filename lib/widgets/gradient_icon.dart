import 'package:flutter/material.dart';

/// 渐变色图标组件
class GradientIcon extends StatelessWidget {
  final IconData icon;
  final double size;
  final List<Color> gradientColors;
  final bool isSelected;

  const GradientIcon({
    super.key,
    required this.icon,
    required this.size,
    required this.gradientColors,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (!isSelected) {
      // 未选中时显示主题适配的灰色图标
      final colorScheme = Theme.of(context).colorScheme;
      return Icon(icon, size: size, color: colorScheme.onSurfaceVariant);
    }

    // 选中时显示渐变色图标 - 使用ColorFiltered的简单渐变方案
    // 由于ColorFiltered能完全覆盖，我们使用混合色来模拟渐变效果
    final blendedColor = Color.lerp(gradientColors[0], gradientColors[1], 0.5);

    return ColorFiltered(
      colorFilter: ColorFilter.mode(
        blendedColor ?? gradientColors[0],
        BlendMode.srcIn,
      ),
      child: Icon(icon, size: size, color: Colors.white),
    );
  }
}
