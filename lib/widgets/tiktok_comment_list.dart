import 'package:flutter/material.dart';
import '../models/spot_comment.dart';
import '../services/service_locator.dart';
import 'tiktok_style_comment.dart';
import 'snackbar.dart';

/// 抖音风格的评论列表组件
class TikTokCommentList extends StatefulWidget {
  final String spotId;
  final List<SpotComment>? initialComments;
  final VoidCallback? onRefresh;
  final bool? initialIsLoading;
  final Function(SpotComment)? onNewComment; // 新增：通知父组件有新评论
  final Function(SpotComment)? onReply; // 新增：回复回调

  const TikTokCommentList({
    super.key,
    required this.spotId,
    this.initialComments,
    this.onRefresh,
    this.initialIsLoading,
    this.onNewComment,
    this.onReply,
  });

  @override
  State<TikTokCommentList> createState() => TikTokCommentListState();
}

class TikTokCommentListState extends State<TikTokCommentList> {
  final Map<String, List<SpotComment>> _repliesCache = {};
  final Map<String, bool> _expandedComments = {};
  final Map<String, bool> _loadingReplies = {};

  // 内部管理的评论列表和加载状态
  List<SpotComment> _comments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 初始化状态
    _comments = widget.initialComments ?? [];
    _isLoading = widget.initialIsLoading ?? false;

    // 如果没有初始评论，则加载评论
    if (_comments.isEmpty && !_isLoading) {
      _loadComments();
    }
  }

  /// 加载评论列表
  Future<void> _loadComments() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('💬 [评论列表] 开始加载评论列表');
      final comments = await Services.spotComment.getSpotComments(widget.spotId);

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoading = false;
        });
        debugPrint('✅ [评论列表] 评论加载完成，共 ${_comments.length} 条');
      }
    } catch (e) {
      debugPrint('❌ [评论列表] 加载评论失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 添加新评论到列表
  void addComment(SpotComment comment) {
    if (mounted) {
      setState(() {
        _comments = [comment, ..._comments];
      });
      debugPrint('🎯 [评论列表] 本地添加新评论: ${comment.content}');
    }
  }

  /// 提交新评论
  Future<bool> submitComment(String content, {SpotComment? replyToComment}) async {
    try {
      debugPrint('💬 [评论列表] 开始提交${replyToComment != null ? "回复" : "评论"}');

      final success = await Services.spotComment.addComment(
        spotId: widget.spotId,
        content: content,
        parentCommentId: replyToComment?.id,
        replyToUserId: replyToComment?.userId,
        replyToUsername: replyToComment?.username,
      );

      if (success) {
        // 重新加载评论列表以获取最新数据
        await _loadComments();
        debugPrint('✅ [评论列表] ${replyToComment != null ? "回复" : "评论"}提交成功，已刷新列表');
        return true;
      } else {
        debugPrint('❌ [评论列表] ${replyToComment != null ? "回复" : "评论"}提交失败');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [评论列表] ${replyToComment != null ? "回复" : "评论"}提交异常: $e');
      return false;
    }
  }

  /// 处理回复评论
  Future<void> _handleReply(SpotComment comment) async {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      SnackBarService.showWarning(context, '请先登录后再回复评论');
      return;
    }

    // 调用父组件的回复回调
    widget.onReply?.call(comment);
  }



  /// 加载回复列表
  Future<void> _loadReplies(String commentId) async {
    if (_loadingReplies[commentId] == true) return;

    setState(() {
      _loadingReplies[commentId] = true;
    });

    try {
      final replies = await Services.spotComment.getCommentReplies(commentId);
      setState(() {
        _repliesCache[commentId] = replies;
        _loadingReplies[commentId] = false;
      });
    } catch (e) {
      debugPrint('❌ [评论列表] 加载回复失败: $e');
      setState(() {
        _loadingReplies[commentId] = false;
      });
    }
  }

  /// 切换回复展开状态
  void _toggleReplies(String commentId) {
    final isExpanded = _expandedComments[commentId] ?? false;
    
    if (!isExpanded) {
      // 如果要展开，先加载回复
      _loadReplies(commentId);
    }
    
    setState(() {
      _expandedComments[commentId] = !isExpanded;
    });
  }

  /// 处理点赞变化
  void _handleLikeChanged() {
    // 点赞变化只需要更新本地状态，不需要重新加载整个评论列表
    // 这样可以避免触发不必要的页面重建和图片重新加载
    debugPrint('💖 [评论列表] 点赞状态已更新，无需刷新');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_comments.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                '还没有评论',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '快来发表第一条评论吧！',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _comments.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        color: Colors.grey.shade200,
      ),
      itemBuilder: (context, index) {
        final comment = _comments[index];
        final isExpanded = _expandedComments[comment.id] ?? false;
        final replies = _repliesCache[comment.id] ?? [];
        final isLoadingReplies = _loadingReplies[comment.id] ?? false;

        return Column(
          children: [
            GestureDetector(
              onTap: () {
                if (comment.repliesCount > 0) {
                  _toggleReplies(comment.id);
                }
              },
              child: TikTokStyleComment(
                comment: comment,
                onReply: () => _handleReply(comment),
                onLikeChanged: _handleLikeChanged,
                showReplies: isExpanded,
                replies: replies,
                onLoadMoreReplies: replies.length < comment.repliesCount
                    ? () => _loadReplies(comment.id)
                    : null,
              ),
            ),
            
            // 回复加载指示器
            if (isLoadingReplies)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 56, vertical: 8),
                child: Row(
                  children: [
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade400),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '加载回复中...',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}
