import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 钓鱼时间选择器
///
/// 用于选择约钓活动的开始时间和持续时长
class FishingTimeSelector extends StatefulWidget {
  /// 当前选中的开始时间
  final DateTime? selectedTime;

  /// 当前选中的持续时长（小时）
  final double? selectedDuration;

  /// 时间变更回调
  final Function(DateTime, double) onTimeChanged;

  /// 是否必填
  final bool required;

  const FishingTimeSelector({
    super.key,
    this.selectedTime,
    this.selectedDuration,
    required this.onTimeChanged,
    this.required = true,
  });

  @override
  State<FishingTimeSelector> createState() => _FishingTimeSelectorState();
}

class _FishingTimeSelectorState extends State<FishingTimeSelector> {
  DateTime? _selectedDateTime;
  double _selectedDuration = 2.0; // 默认2小时
  final DateFormat _dateFormat = DateFormat('yyyy年MM月dd日');
  final DateFormat _timeFormat = DateFormat('HH:mm');

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.selectedTime ?? _getDefaultTime();
    _selectedDuration = widget.selectedDuration ?? 2.0;
  }

  /// 获取默认时间（当前时间后1小时，整点）
  DateTime _getDefaultTime() {
    final now = DateTime.now();
    final nextHour = now.add(const Duration(hours: 1));
    return DateTime(
      nextHour.year,
      nextHour.month,
      nextHour.day,
      nextHour.hour,
      0, // 分钟设为0，整点时间
    );
  }

  /// 显示日期选择器
  Future<void> _selectDate() async {
    final now = DateTime.now();
    final initialDate = _selectedDateTime ?? _getDefaultTime();

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now, // 不能选择过去的日期
      lastDate: now.add(const Duration(days: 30)), // 最多30天后
      locale: const Locale('zh', 'CN'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.blue.shade600,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          _selectedDateTime?.hour ?? _getDefaultTime().hour,
          _selectedDateTime?.minute ?? 0,
        );
      });
      widget.onTimeChanged(_selectedDateTime!, _selectedDuration);
    }
  }

  /// 显示时间选择器
  Future<void> _selectTime() async {
    final initialTime = TimeOfDay.fromDateTime(
      _selectedDateTime ?? _getDefaultTime(),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Colors.blue.shade600,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedTime != null) {
      final currentDate = _selectedDateTime ?? _getDefaultTime();
      setState(() {
        _selectedDateTime = DateTime(
          currentDate.year,
          currentDate.month,
          currentDate.day,
          selectedTime.hour,
          selectedTime.minute,
        );
      });
      widget.onTimeChanged(_selectedDateTime!, _selectedDuration);
    }
  }

  /// 获取时间状态描述
  String _getTimeStatusDescription() {
    if (_selectedDateTime == null) return '';

    final now = DateTime.now();
    final difference = _selectedDateTime!.difference(now);

    if (difference.inMinutes < 0) {
      return '已过期';
    } else if (difference.inHours < 1) {
      return '即将开始';
    } else if (difference.inDays < 1) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '明天';
    } else {
      return '${difference.inDays}天后';
    }
  }

  /// 获取时间状态颜色
  Color _getTimeStatusColor() {
    if (_selectedDateTime == null) return Colors.grey;

    final now = DateTime.now();
    final difference = _selectedDateTime!.difference(now);

    if (difference.inMinutes < 0) {
      return Colors.red;
    } else if (difference.inHours < 1) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// 格式化结束时间
  String _formatEndTime() {
    if (_selectedDateTime == null) return '';

    final endTime = _selectedDateTime!.add(
      Duration(
        hours: _selectedDuration.toInt(),
        minutes: ((_selectedDuration % 1) * 60).toInt(),
      ),
    );

    return _timeFormat.format(endTime);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            const Icon(Icons.access_time, size: 20, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              '钓鱼时间',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            if (widget.required)
              const Text(
                ' *',
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // 时间选择按钮
        Row(
          children: [
            // 日期选择
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: _selectDate,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1.5),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 18,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedDateTime != null
                              ? _dateFormat.format(_selectedDateTime!)
                              : '选择日期',
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                _selectedDateTime != null
                                    ? Colors.black87
                                    : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 时间选择
            Expanded(
              flex: 1,
              child: InkWell(
                onTap: _selectTime,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1.5),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 18,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedDateTime != null
                              ? _timeFormat.format(_selectedDateTime!)
                              : '时间',
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                _selectedDateTime != null
                                    ? Colors.black87
                                    : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 持续时长选择
        Row(
          children: [
            const Icon(Icons.timer, size: 20, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              '持续时长',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 持续时长选择按钮
        Row(
          children: [
            for (double duration in [1.0, 2.0, 3.0, 4.0, 6.0, 8.0])
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: duration == 8.0 ? 0 : 8),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedDuration = duration;
                      });
                      if (_selectedDateTime != null) {
                        widget.onTimeChanged(
                          _selectedDateTime!,
                          _selectedDuration,
                        );
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color:
                            _selectedDuration == duration
                                ? Colors.blue.shade50
                                : Colors.white,
                        border: Border.all(
                          color:
                              _selectedDuration == duration
                                  ? Colors.blue.shade300
                                  : Colors.grey.shade300,
                          width: _selectedDuration == duration ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${duration.toInt()}h',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight:
                              _selectedDuration == duration
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                          color:
                              _selectedDuration == duration
                                  ? Colors.blue.shade700
                                  : Colors.black87,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 8),

        // 结束时间显示
        if (_selectedDateTime != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200, width: 1),
            ),
            child: Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '结束时间: ${_formatEndTime()}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                ),
              ],
            ),
          ),

        const SizedBox(height: 8),

        // 时间状态提示
        if (_selectedDateTime != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getTimeStatusColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _getTimeStatusColor().withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.info_outline,
                  size: 14,
                  color: _getTimeStatusColor(),
                ),
                const SizedBox(width: 4),
                Text(
                  _getTimeStatusDescription(),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getTimeStatusColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 8),

        // 使用提示
        Text(
          '选择您计划开始钓鱼的时间，活动将在钓鱼时间后2小时自动结束',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            height: 1.3,
          ),
        ),
      ],
    );
  }
}
