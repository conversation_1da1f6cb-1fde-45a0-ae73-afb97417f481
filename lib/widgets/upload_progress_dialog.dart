import 'package:flutter/material.dart';

/// 图片上传进度对话框
class UploadProgressDialog extends StatefulWidget {
  final int totalImages;
  final Stream<UploadProgress> progressStream;
  final VoidCallback? onCancel;

  const UploadProgressDialog({
    super.key,
    required this.totalImages,
    required this.progressStream,
    this.onCancel,
  });

  @override
  State<UploadProgressDialog> createState() => _UploadProgressDialogState();
}

class _UploadProgressDialogState extends State<UploadProgressDialog> {
  int _uploadedCount = 0;
  int _failedCount = 0;
  String _currentFileName = '';
  bool _isCompleted = false;
  List<String> _failedFiles = [];

  @override
  void initState() {
    super.initState();
    _listenToProgress();
  }

  void _listenToProgress() {
    widget.progressStream.listen(
      (progress) {
        if (mounted) {
          setState(() {
            _uploadedCount = progress.uploadedCount;
            _failedCount = progress.failedCount;
            _currentFileName = progress.currentFileName;
            _isCompleted = progress.isCompleted;
            _failedFiles = progress.failedFiles;
          });

          // 如果上传完成，延迟关闭对话框
          if (_isCompleted) {
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                Navigator.of(context).pop();
              }
            });
          }
        }
      },
      onError: (error) {
        if (mounted) {
          setState(() {
            _isCompleted = true;
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final successCount = _uploadedCount;
    final totalCount = widget.totalImages;
    final progress = totalCount > 0 ? successCount / totalCount : 0.0;

    return PopScope(
      canPop: _isCompleted || widget.onCancel != null,
      child: AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.cloud_upload, color: Colors.blue),
            const SizedBox(width: 8),
            Text(_isCompleted ? '上传完成' : '正在上传图片'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 总体进度条
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                _isCompleted
                    ? (_failedCount > 0 ? Colors.orange : Colors.green)
                    : Colors.blue,
              ),
            ),
            const SizedBox(height: 16),

            // 进度文字
            Text(
              '进度: $successCount/$totalCount',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // 当前上传文件
            if (!_isCompleted && _currentFileName.isNotEmpty) ...[
              Text(
                '正在上传: $_currentFileName',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
            ],

            // 成功和失败统计
            if (_isCompleted) ...[
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 4),
                  Text('成功: $successCount'),
                ],
              ),
              if (_failedCount > 0) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 16),
                    const SizedBox(width: 4),
                    Text('失败: $_failedCount'),
                  ],
                ),
              ],
            ],

            // 失败文件列表
            if (_failedFiles.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '上传失败的文件:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                constraints: const BoxConstraints(maxHeight: 100),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _failedFiles.map((fileName) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          '• $fileName',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.red.shade700,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          // 取消按钮（仅在未完成时显示）
          if (!_isCompleted && widget.onCancel != null)
            TextButton(
              onPressed: widget.onCancel,
              child: const Text('取消'),
            ),

          // 确定按钮（仅在完成时显示）
          if (_isCompleted)
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
        ],
      ),
    );
  }
}

/// 上传进度数据类
class UploadProgress {
  final int uploadedCount;
  final int failedCount;
  final String currentFileName;
  final bool isCompleted;
  final List<String> failedFiles;

  const UploadProgress({
    required this.uploadedCount,
    required this.failedCount,
    required this.currentFileName,
    required this.isCompleted,
    required this.failedFiles,
  });

  UploadProgress copyWith({
    int? uploadedCount,
    int? failedCount,
    String? currentFileName,
    bool? isCompleted,
    List<String>? failedFiles,
  }) {
    return UploadProgress(
      uploadedCount: uploadedCount ?? this.uploadedCount,
      failedCount: failedCount ?? this.failedCount,
      currentFileName: currentFileName ?? this.currentFileName,
      isCompleted: isCompleted ?? this.isCompleted,
      failedFiles: failedFiles ?? this.failedFiles,
    );
  }
}
