import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';

import '../models/fishing_activity.dart';
import '../theme/unified_theme.dart';
import '../services/service_locator.dart';

// 导入组件
import 'add_spot_form/spot_name_input.dart';
import 'add_spot_form/spot_description_input.dart';
import 'add_spot_form/fish_type_selector.dart';
import 'add_spot_form/image_upload_widget.dart';
import 'add_spot_form/image_upload_manager.dart';
import 'add_activity_form/fishing_time_selector.dart';
import 'location_status_widget.dart';

/// 分屏添加钓鱼活动组件
class SplitScreenAddActivity extends StatefulWidget {
  /// 活动位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加活动回调
  final Function(FishingActivity) onActivityAdded;

  /// 建议的活动名称
  final String? suggestedName;

  const SplitScreenAddActivity({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onActivityAdded,
    this.suggestedName,
  });

  @override
  State<SplitScreenAddActivity> createState() => _SplitScreenAddActivityState();
}

class _SplitScreenAddActivityState extends State<SplitScreenAddActivity> {
  final _formKey = GlobalKey<FormState>();
  final _imageUploadManager = ImageUploadManager();

  // 表单控制器
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // 拖拽相关状态
  DraggableScrollableController? _dragController;
  double _currentSize = 0.5; // 当前高度比例

  // 表单状态
  String _selectedFishType = 'carp'; // 默认鲤鱼
  DateTime? _selectedFishingTime;
  double _selectedDuration = 2.0; // 默认2小时
  final List<ImageUploadItem> _selectedImages = [];

  // 位置验证状态
  Position? _publishLocation;

  // UI状态
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _dragController = DraggableScrollableController();
    // 如果有建议的活动名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }

    // 设置默认钓鱼时间（1小时后）
    _selectedFishingTime = DateTime.now().add(const Duration(hours: 1));
  }

  @override
  void didUpdateWidget(SplitScreenAddActivity oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _dragController?.dispose();
    super.dispose();
  }

  /// 处理图片添加
  void _handleImagesAdded(List<ImageUploadItem> newImages) {
    setState(() {
      _selectedImages.addAll(newImages);
    });
  }

  /// 处理图片移除
  void _handleImageRemoved(int index) {
    setState(() {
      if (index >= 0 && index < _selectedImages.length) {
        _selectedImages.removeAt(index);
      }
    });
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return !_isSubmitting &&
        !_imageUploadManager.hasUploadingImages(_selectedImages) &&
        _nameController.text.trim().isNotEmpty &&
        _selectedFishingTime != null;
  }

  /// 处理提交
  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedFishingTime == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请选择钓鱼时间')));
      return;
    }

    // 检查钓鱼时间是否在未来
    if (_selectedFishingTime!.isBefore(DateTime.now())) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('钓鱼时间不能是过去的时间')));
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final user = Services.auth.currentUser;
      if (user == null) {
        throw Exception('用户未登录');
      }

      // 等待所有图片上传完成
      if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('请等待图片上传完成')));
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      final clientGeneratedId = const Uuid().v4();

      final activity = FishingActivity(
        id: clientGeneratedId,
        title: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        location: {
          'lat': widget.location.latitude,
          'lon': widget.location.longitude,
        },
        startTime: _selectedFishingTime!,
        duration: _selectedDuration,
        maxParticipants: 10, // 默认最多10人
        currentParticipants: 1, // 创建者
        creatorId: user.id,
        creatorName: user.username,
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      debugPrint('🔍 [添加活动] 准备提交活动数据');
      debugPrint('🔍 [添加活动] 活动标题: ${activity.title}');
      debugPrint('🔍 [添加活动] 开始时间: ${activity.startTime}');
      debugPrint('🔍 [添加活动] 持续时长: ${activity.duration}小时');

      // 调用服务添加活动
      final result = await Services.fishingActivity.addActivity(
        activity,
        publishLocation:
            _publishLocation != null
                ? {
                  'lat': _publishLocation!.latitude,
                  'lon': _publishLocation!.longitude,
                }
                : null,
      );

      if (result != null) {
        debugPrint('✅ [添加活动] 活动添加成功');

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '约钓活动发布成功！\n钓鱼时间：${_formatDateTime(_selectedFishingTime!)}',
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }

        // 调用成功回调
        widget.onActivityAdded(result);
      } else {
        throw Exception('活动创建失败');
      }
    } catch (e) {
      debugPrint('❌ [添加活动] 提交失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('发布失败: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 构建表单卡片
  Widget _buildFormCard({required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(color: Colors.grey.shade200, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  /// 获取提交按钮文本
  String _getSubmitButtonText() {
    if (_isSubmitting) {
      return '发布中...';
    }

    if (_imageUploadManager.hasUploadingImages(_selectedImages)) {
      return '图片上传中...';
    }

    return '发布约钓';
  }

  /// 获取提交按钮图标
  Widget _getSubmitButtonIcon() {
    if (_isSubmitting ||
        _imageUploadManager.hasUploadingImages(_selectedImages)) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: const Icon(Icons.group, color: Colors.white, size: 18),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      controller: _dragController,
      initialChildSize: 0.5,
      minChildSize: 0.3,
      maxChildSize: 0.85,
      snap: true,
      snapSizes: const [0.3, 0.5, 0.85],
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 32,
                offset: const Offset(0, -8),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child:
                _isSubmitting
                    ? const Center(child: CircularProgressIndicator())
                    : Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 可拖拽的顶部区域
                          GestureDetector(
                            onPanUpdate: (details) {
                              _handleTopDrag(details);
                            },
                            onPanEnd: (details) {
                              _handleTopDragEnd(details);
                            },
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              child: Center(
                                child: Container(
                                  margin: const EdgeInsets.only(
                                    top: 12,
                                    bottom: 16,
                                  ),
                                  width: 48,
                                  height: 5,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.grey.shade300,
                                        Colors.grey.shade400,
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(3),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 4,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // 标题栏
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                            child: Row(
                              children: [
                                const Text(
                                  '一起去钓鱼',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.grey,
                                    size: 24,
                                  ),
                                  onPressed: widget.onClose,
                                ),
                              ],
                            ),
                          ),

                          // 坐标信息
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              children: [
                                LocationStatusWidget(
                                  size: 20,
                                  autoStart: true,
                                  timeoutSeconds: 11,
                                  showSuccessSnackBar: true,
                                  onLocationUpdate: (data) {
                                    if (data.location != null) {
                                      setState(() {
                                        _publishLocation = Position(
                                          latitude: data.location!.latitude,
                                          longitude: data.location!.longitude,
                                          timestamp: DateTime.now(),
                                          accuracy: 0,
                                          altitude: 0,
                                          altitudeAccuracy: 0,
                                          heading: 0,
                                          headingAccuracy: 0,
                                          speed: 0,
                                          speedAccuracy: 0,
                                        );
                                      });
                                    }
                                  },
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  '坐标位置',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // 表单内容区域
                          Expanded(
                            child: SingleChildScrollView(
                              controller: scrollController,
                              physics: const BouncingScrollPhysics(),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 活动名称
                                    _buildFormCard(
                                      child: SpotNameInput(
                                        controller: _nameController,
                                        location: widget.location,
                                        validator: (value) {
                                          if (value == null ||
                                              value.trim().isEmpty) {
                                            return '请输入活动名称';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 钓鱼时间
                                    _buildFormCard(
                                      child: FishingTimeSelector(
                                        selectedTime: _selectedFishingTime,
                                        selectedDuration: _selectedDuration,
                                        onTimeChanged: (time, duration) {
                                          setState(() {
                                            _selectedFishingTime = time;
                                            _selectedDuration = duration;
                                          });
                                        },
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 图片上传
                                    _buildFormCard(
                                      child: ImageUploadWidget(
                                        selectedImages: _selectedImages,
                                        onImagesAdded: _handleImagesAdded,
                                        onImageRemoved: _handleImageRemoved,
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 活动描述
                                    _buildFormCard(
                                      child: SpotDescriptionInput(
                                        controller: _descriptionController,
                                      ),
                                    ),

                                    const SizedBox(height: AppTheme.spacingM),

                                    // 鱼种选择
                                    _buildFormCard(
                                      child: FishTypeSelector(
                                        selectedFishType: _selectedFishType,
                                        onChanged:
                                            (value) => setState(
                                              () => _selectedFishType = value,
                                            ),
                                      ),
                                    ),

                                    const SizedBox(height: 32),

                                    // 发布按钮
                                    Container(
                                      width: double.infinity,
                                      height: 56,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors:
                                              _isSubmitting ||
                                                      _imageUploadManager
                                                          .hasUploadingImages(
                                                            _selectedImages,
                                                          )
                                                  ? [
                                                    Colors.grey.shade300,
                                                    Colors.grey.shade400,
                                                  ]
                                                  : [
                                                    Colors.blue.shade400,
                                                    Colors.blue.shade600,
                                                  ],
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow:
                                            _isSubmitting ||
                                                    _imageUploadManager
                                                        .hasUploadingImages(
                                                          _selectedImages,
                                                        )
                                                ? []
                                                : [
                                                  BoxShadow(
                                                    color: Colors.blue
                                                        .withValues(alpha: 0.4),
                                                    blurRadius: 16,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed:
                                            _canSubmit() ? _handleSubmit : null,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.transparent,
                                          shadowColor: Colors.transparent,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            _getSubmitButtonIcon(),
                                            const SizedBox(width: 8),
                                            Text(
                                              _getSubmitButtonText(),
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 32),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  /// 处理顶部拖拽
  void _handleTopDrag(DragUpdateDetails details) {
    if (_dragController != null && _dragController!.isAttached) {
      final screenHeight = MediaQuery.of(context).size.height;
      final deltaY = details.delta.dy;
      final deltaSize = -deltaY / screenHeight; // 向上拖拽为正值
      
      final newSize = (_currentSize + deltaSize).clamp(0.3, 0.85);
      _dragController!.animateTo(
        newSize,
        duration: const Duration(milliseconds: 1),
        curve: Curves.linear,
      );
      _currentSize = newSize;
    }
  }

  /// 处理拖拽结束，实现吸附效果
  void _handleTopDragEnd(DragEndDetails details) {
    if (_dragController != null && _dragController!.isAttached) {
      // 计算最近的吸附点
      const snapSizes = [0.3, 0.5, 0.85];
      double targetSize = snapSizes.reduce((a, b) => 
        (a - _currentSize).abs() < (b - _currentSize).abs() ? a : b
      );
      
      // 考虑拖拽速度
      final velocity = details.velocity.pixelsPerSecond.dy;
      if (velocity.abs() > 500) {
        if (velocity < 0) {
          // 向上快速拖拽，选择更大的尺寸
          targetSize = snapSizes.where((size) => size > _currentSize).isNotEmpty
              ? snapSizes.where((size) => size > _currentSize).first
              : snapSizes.last;
        } else {
          // 向下快速拖拽，选择更小的尺寸
          targetSize = snapSizes.where((size) => size < _currentSize).isNotEmpty
              ? snapSizes.where((size) => size < _currentSize).last
              : snapSizes.first;
        }
      }
      
      _dragController!.animateTo(
        targetSize,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
      _currentSize = targetSize;
    }
  }
}
