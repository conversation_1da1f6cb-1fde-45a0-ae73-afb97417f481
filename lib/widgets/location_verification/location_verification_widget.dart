import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import '../../services/location_verification_service.dart';

/// GPS位置验证状态
enum LocationVerificationStatus {
  loading,    // 获取位置中
  verified,   // 位置验证成功（在范围内）
  notOnSite,  // 位置验证成功（不在范围内）
  error,      // 位置获取失败
}

/// 位置验证回调数据
class LocationVerificationData {
  final LocationVerificationStatus status;
  final Position? position;
  final double? distanceToTarget;
  final bool isOnSite;
  final String? errorMessage;

  const LocationVerificationData({
    required this.status,
    this.position,
    this.distanceToTarget,
    required this.isOnSite,
    this.errorMessage,
  });
}

/// 位置验证样式配置
class LocationVerificationStyle {
  final Color loadingColor;
  final Color verifiedColor;
  final Color notOnSiteColor;
  final Color errorColor;
  final double borderRadius;
  final EdgeInsets padding;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;

  const LocationVerificationStyle({
    this.loadingColor = Colors.blue,
    this.verifiedColor = Colors.green,
    this.notOnSiteColor = Colors.orange,
    this.errorColor = Colors.grey,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.all(12),
    this.titleStyle,
    this.descriptionStyle,
  });
}

/// GPS位置验证Widget
/// 
/// 功能：
/// - 实时GPS监控
/// - 位置状态显示
/// - 距离计算和验证
/// - 错误处理和重试
class LocationVerificationWidget extends StatefulWidget {
  /// 目标位置
  final LatLng targetLocation;
  
  /// 验证距离阈值（米）
  final double thresholdMeters;
  
  /// 位置更新回调
  final Function(LocationVerificationData data)? onLocationUpdate;
  
  /// 是否显示距离信息
  final bool showDistance;
  
  /// 是否允许重试
  final bool allowRetry;
  
  /// 自定义样式
  final LocationVerificationStyle style;
  
  /// 是否自动开始监控
  final bool autoStart;

  const LocationVerificationWidget({
    super.key,
    required this.targetLocation,
    this.thresholdMeters = 50.0,
    this.onLocationUpdate,
    this.showDistance = true,
    this.allowRetry = true,
    this.style = const LocationVerificationStyle(),
    this.autoStart = true,
  });

  @override
  State<LocationVerificationWidget> createState() => _LocationVerificationWidgetState();
}

class _LocationVerificationWidgetState extends State<LocationVerificationWidget> {
  LocationVerificationStatus _status = LocationVerificationStatus.loading;
  Position? _currentPosition;
  double? _distanceToTarget;
  bool _isOnSite = false;
  String? _errorMessage;
  StreamSubscription<Position>? _locationSubscription;

  @override
  void initState() {
    super.initState();
    if (widget.autoStart) {
      _startLocationVerification();
    }
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(LocationVerificationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果目标位置发生变化，重新计算验证状态
    if (widget.targetLocation != oldWidget.targetLocation) {
      _updateVerificationStatus();
    }
  }

  /// 开始位置验证
  Future<void> _startLocationVerification() async {
    debugPrint('📍 [位置验证模块] 开始位置验证');
    
    try {
      setState(() {
        _status = LocationVerificationStatus.loading;
        _errorMessage = null;
      });

      // 获取当前位置
      _currentPosition = await LocationVerificationService.getCurrentPosition();

      if (_currentPosition != null) {
        _updateVerificationStatus();

        // 开始监听位置变化
        _locationSubscription = LocationVerificationService.listenToLocationChanges(
          widget.targetLocation,
          (isOnSite, position) {
            if (mounted) {
              _updateLocationStatus(isOnSite, position);
            }
          },
        );

        debugPrint('📍 [位置验证模块] 位置验证启动成功');
      } else {
        throw Exception('无法获取当前位置');
      }
    } catch (e) {
      debugPrint('❌ [位置验证模块] 位置验证失败: $e');
      setState(() {
        _status = LocationVerificationStatus.error;
        _errorMessage = e.toString();
        _currentPosition = null;
        _isOnSite = false;
        _distanceToTarget = null;
      });
      _notifyLocationUpdate();
    }
  }

  /// 更新位置状态
  void _updateLocationStatus(bool isOnSite, Position position) {
    setState(() {
      _isOnSite = isOnSite;
      _currentPosition = position;
      _updateVerificationStatus();
    });
    
    debugPrint('📍 [位置验证模块] 位置更新 - 距离: ${_distanceToTarget?.toStringAsFixed(1)}米，实地: ${isOnSite ? "是" : "否"}');
  }

  /// 更新验证状态
  void _updateVerificationStatus() {
    if (_currentPosition != null) {
      // 计算距离
      _distanceToTarget = Geolocator.distanceBetween(
        widget.targetLocation.latitude,
        widget.targetLocation.longitude,
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );
      
      // 检查是否在范围内
      _isOnSite = _distanceToTarget! <= widget.thresholdMeters;
      
      // 更新状态
      _status = _isOnSite 
          ? LocationVerificationStatus.verified 
          : LocationVerificationStatus.notOnSite;
      
      _notifyLocationUpdate();
    }
  }

  /// 通知位置更新
  void _notifyLocationUpdate() {
    if (widget.onLocationUpdate != null) {
      final data = LocationVerificationData(
        status: _status,
        position: _currentPosition,
        distanceToTarget: _distanceToTarget,
        isOnSite: _isOnSite,
        errorMessage: _errorMessage,
      );
      widget.onLocationUpdate!(data);
    }
  }

  /// 刷新位置
  void _refreshLocation() {
    debugPrint('📍 [位置验证模块] 用户请求刷新位置');
    _startLocationVerification();
  }

  /// 手动开始验证（用于非自动开始的情况）
  void startVerification() {
    _startLocationVerification();
  }

  /// 停止验证
  void stopVerification() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.style.padding,
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(widget.style.borderRadius),
        border: Border.all(color: _getStatusColor()),
      ),
      child: Row(
        children: [
          _getStatusIcon(),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusTitle(),
                  style: widget.style.titleStyle ?? TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getStatusColor(),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getStatusDescription(),
                  style: widget.style.descriptionStyle ?? const TextStyle(
                    fontSize: 12, 
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (_canRefresh())
            TextButton(
              onPressed: _refreshLocation,
              child: const Text('重试', style: TextStyle(fontSize: 12)),
            ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (_status) {
      case LocationVerificationStatus.loading:
        return widget.style.loadingColor;
      case LocationVerificationStatus.verified:
        return widget.style.verifiedColor;
      case LocationVerificationStatus.notOnSite:
        return widget.style.notOnSiteColor;
      case LocationVerificationStatus.error:
        return widget.style.errorColor;
    }
  }

  /// 获取状态图标
  Widget _getStatusIcon() {
    switch (_status) {
      case LocationVerificationStatus.loading:
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case LocationVerificationStatus.verified:
        return Icon(Icons.location_on, color: widget.style.verifiedColor, size: 16);
      case LocationVerificationStatus.notOnSite:
        return Icon(Icons.location_off, color: widget.style.notOnSiteColor, size: 16);
      case LocationVerificationStatus.error:
        return Icon(Icons.location_disabled, color: widget.style.errorColor, size: 16);
    }
  }

  /// 获取状态标题
  String _getStatusTitle() {
    switch (_status) {
      case LocationVerificationStatus.loading:
        return '获取位置中...';
      case LocationVerificationStatus.verified:
        return '实地验证通过';
      case LocationVerificationStatus.notOnSite:
        return '非实地位置';
      case LocationVerificationStatus.error:
        return '位置获取失败';
    }
  }

  /// 获取状态描述
  String _getStatusDescription() {
    switch (_status) {
      case LocationVerificationStatus.loading:
        return '正在获取GPS位置信息...';
      case LocationVerificationStatus.verified:
      case LocationVerificationStatus.notOnSite:
        if (widget.showDistance && _distanceToTarget != null) {
          return '距离目标 ${_distanceToTarget!.toStringAsFixed(1)} 米';
        }
        return '位置验证完成';
      case LocationVerificationStatus.error:
        return _errorMessage ?? '无法获取GPS位置信息';
    }
  }

  /// 是否可以刷新
  bool _canRefresh() {
    return widget.allowRetry && 
           (_status == LocationVerificationStatus.error || 
            _status == LocationVerificationStatus.loading);
  }
}
