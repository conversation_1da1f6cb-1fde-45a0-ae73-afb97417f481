import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import '../../services/location_verification_service.dart';
import 'location_verification_widget.dart';

/// GPS位置验证控制器
/// 
/// 提供程序化控制GPS位置验证的功能
class LocationVerificationController extends ChangeNotifier {
  LatLng? _targetLocation;
  double _thresholdMeters = 50.0;
  
  LocationVerificationStatus _status = LocationVerificationStatus.loading;
  Position? _currentPosition;
  double? _distanceToTarget;
  bool _isOnSite = false;
  String? _errorMessage;
  StreamSubscription<Position>? _locationSubscription;
  
  // 回调函数
  Function(LocationVerificationData data)? _onLocationUpdate;

  /// 当前状态
  LocationVerificationStatus get status => _status;
  
  /// 当前位置
  Position? get currentPosition => _currentPosition;
  
  /// 距离目标的距离
  double? get distanceToTarget => _distanceToTarget;
  
  /// 是否在目标范围内
  bool get isOnSite => _isOnSite;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 目标位置
  LatLng? get targetLocation => _targetLocation;
  
  /// 验证距离阈值
  double get thresholdMeters => _thresholdMeters;

  /// 设置位置更新回调
  void setOnLocationUpdate(Function(LocationVerificationData data)? callback) {
    _onLocationUpdate = callback;
  }

  /// 配置验证参数
  void configure({
    required LatLng targetLocation,
    double thresholdMeters = 50.0,
  }) {
    _targetLocation = targetLocation;
    _thresholdMeters = thresholdMeters;
    notifyListeners();
  }

  /// 开始位置验证
  Future<void> startVerification() async {
    if (_targetLocation == null) {
      throw Exception('目标位置未设置，请先调用configure()方法');
    }

    debugPrint('📍 [位置验证控制器] 开始位置验证');
    
    try {
      _updateStatus(
        status: LocationVerificationStatus.loading,
        errorMessage: null,
      );

      // 获取当前位置
      _currentPosition = await LocationVerificationService.getCurrentPosition();

      if (_currentPosition != null) {
        _updateVerificationStatus();

        // 开始监听位置变化
        _locationSubscription = LocationVerificationService.listenToLocationChanges(
          _targetLocation!,
          (isOnSite, position) {
            _updateLocationStatus(isOnSite, position);
          },
        );

        debugPrint('📍 [位置验证控制器] 位置验证启动成功');
      } else {
        throw Exception('无法获取当前位置');
      }
    } catch (e) {
      debugPrint('❌ [位置验证控制器] 位置验证失败: $e');
      _updateStatus(
        status: LocationVerificationStatus.error,
        errorMessage: e.toString(),
        position: null,
        isOnSite: false,
        distanceToTarget: null,
      );
    }
  }

  /// 停止位置验证
  void stopVerification() {
    debugPrint('📍 [位置验证控制器] 停止位置验证');
    _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  /// 刷新位置
  Future<void> refreshLocation() async {
    debugPrint('📍 [位置验证控制器] 刷新位置');
    await startVerification();
  }

  /// 更新位置状态
  void _updateLocationStatus(bool isOnSite, Position position) {
    _currentPosition = position;
    _isOnSite = isOnSite;
    _updateVerificationStatus();
    
    debugPrint('📍 [位置验证控制器] 位置更新 - 距离: ${_distanceToTarget?.toStringAsFixed(1)}米，实地: ${isOnSite ? "是" : "否"}');
  }

  /// 更新验证状态
  void _updateVerificationStatus() {
    if (_currentPosition != null && _targetLocation != null) {
      // 计算距离
      _distanceToTarget = Geolocator.distanceBetween(
        _targetLocation!.latitude,
        _targetLocation!.longitude,
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );
      
      // 检查是否在范围内
      _isOnSite = _distanceToTarget! <= _thresholdMeters;
      
      // 更新状态
      _status = _isOnSite 
          ? LocationVerificationStatus.verified 
          : LocationVerificationStatus.notOnSite;
      
      _notifyLocationUpdate();
      notifyListeners();
    }
  }

  /// 更新状态
  void _updateStatus({
    required LocationVerificationStatus status,
    Position? position,
    double? distanceToTarget,
    bool? isOnSite,
    String? errorMessage,
  }) {
    _status = status;
    if (position != null) _currentPosition = position;
    if (distanceToTarget != null) _distanceToTarget = distanceToTarget;
    if (isOnSite != null) _isOnSite = isOnSite;
    if (errorMessage != null) _errorMessage = errorMessage;
    
    _notifyLocationUpdate();
    notifyListeners();
  }

  /// 通知位置更新
  void _notifyLocationUpdate() {
    if (_onLocationUpdate != null) {
      final data = LocationVerificationData(
        status: _status,
        position: _currentPosition,
        distanceToTarget: _distanceToTarget,
        isOnSite: _isOnSite,
        errorMessage: _errorMessage,
      );
      _onLocationUpdate!(data);
    }
  }

  /// 获取当前验证数据
  LocationVerificationData getCurrentData() {
    return LocationVerificationData(
      status: _status,
      position: _currentPosition,
      distanceToTarget: _distanceToTarget,
      isOnSite: _isOnSite,
      errorMessage: _errorMessage,
    );
  }

  @override
  void dispose() {
    stopVerification();
    super.dispose();
  }
}

/// 位置验证控制器的便捷扩展
extension LocationVerificationControllerExtension on LocationVerificationController {
  /// 是否正在加载
  bool get isLoading => status == LocationVerificationStatus.loading;
  
  /// 是否验证成功
  bool get isVerified => status == LocationVerificationStatus.verified;
  
  /// 是否有错误
  bool get hasError => status == LocationVerificationStatus.error;
  
  /// 是否可以重试
  bool get canRetry => hasError || isLoading;
  
  /// 获取状态描述
  String getStatusDescription({bool showDistance = true}) {
    switch (status) {
      case LocationVerificationStatus.loading:
        return '正在获取GPS位置信息...';
      case LocationVerificationStatus.verified:
      case LocationVerificationStatus.notOnSite:
        if (showDistance && distanceToTarget != null) {
          return '距离目标 ${distanceToTarget!.toStringAsFixed(1)} 米';
        }
        return '位置验证完成';
      case LocationVerificationStatus.error:
        return errorMessage ?? '无法获取GPS位置信息';
    }
  }
  
  /// 获取状态标题
  String getStatusTitle() {
    switch (status) {
      case LocationVerificationStatus.loading:
        return '获取位置中...';
      case LocationVerificationStatus.verified:
        return '实地验证通过';
      case LocationVerificationStatus.notOnSite:
        return '非实地位置';
      case LocationVerificationStatus.error:
        return '位置获取失败';
    }
  }
}
