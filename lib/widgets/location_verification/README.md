# GPS位置验证模块

这是一个可复用的GPS位置验证模块，提供完整的位置验证功能，包括实时GPS监控、位置状态显示、距离计算和验证、错误处理和重试等功能。

## 功能特性

- ✅ **实时GPS监控** - 自动获取和监听位置变化
- ✅ **位置状态显示** - 清晰的状态指示器和图标
- ✅ **距离计算** - 精确计算到目标位置的距离
- ✅ **智能验证** - 可配置的距离阈值验证
- ✅ **错误处理** - 优雅处理GPS权限和服务问题
- ✅ **可自定义样式** - 支持自定义颜色、字体等样式
- ✅ **智能按钮** - 根据GPS状态自动调整按钮文本和图标
- ✅ **程序化控制** - 提供控制器进行高级控制

## 组件说明

### 1. LocationVerificationWidget
基础的位置验证显示组件，显示当前GPS状态、距离信息和重试按钮。

### 2. LocationVerificationButton
智能位置验证按钮，根据GPS状态自动调整按钮文本、图标和可用性。

### 3. LocationVerificationController
位置验证控制器，提供程序化控制GPS位置验证的功能。

### 4. LocationVerificationIndicator
简化的位置验证状态指示器，是LocationVerificationWidget的别名。

## 使用示例

### 基础位置验证Widget

```dart
import 'package:latlong2/latlong.dart';
import 'location_verification/location_verification.dart';

LocationVerificationWidget(
  targetLocation: LatLng(29.5763, 120.8082), // 目标位置
  thresholdMeters: 50.0,                      // 验证距离阈值（米）
  onLocationUpdate: (data) {
    print('位置状态: ${data.status}');
    print('距离: ${data.distanceToTarget}米');
    print('是否在范围内: ${data.isOnSite}');
  },
  showDistance: true,                         // 显示距离信息
  allowRetry: true,                          // 允许重试
  style: LocationVerificationStyle(          // 自定义样式
    verifiedColor: Colors.green,
    notOnSiteColor: Colors.orange,
    errorColor: Colors.red,
  ),
)
```

### 智能位置验证按钮

```dart
LocationVerificationButton(
  targetLocation: LatLng(29.5763, 120.8082),
  baseText: '发布钓点',                      // 基础按钮文本
  notOnSiteText: '发布钓点（非实地）',        // 非实地时的文本
  onPressed: () {
    // 处理按钮点击
    print('发布钓点');
  },
  onLocationUpdate: (data) {
    // 处理位置更新
    print('GPS状态: ${data.status}');
  },
  gpsAffectsAvailability: false,             // GPS状态不影响按钮可用性
  additionalAvailabilityCheck: () {         // 额外的可用性检查
    return formIsValid && imagesUploaded;
  },
  showLocationIcon: true,                    // 显示位置图标
)
```

### 使用控制器进行程序化控制

```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late LocationVerificationController _controller;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    _controller = LocationVerificationController();
    _controller.configure(
      targetLocation: LatLng(29.5763, 120.8082),
      thresholdMeters: 50.0,
    );
    
    // 设置回调
    _controller.setOnLocationUpdate((data) {
      print('位置更新: ${data.status}');
    });
    
    // 监听状态变化
    _controller.addListener(() {
      setState(() {
        // 更新UI
      });
    });
    
    // 开始验证
    _controller.startVerification();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('GPS状态: ${_controller.getStatusTitle()}'),
        Text('距离: ${_controller.distanceToTarget?.toStringAsFixed(1) ?? "未知"} 米'),
        Text('是否在范围内: ${_controller.isOnSite ? "是" : "否"}'),
        ElevatedButton(
          onPressed: _controller.canRetry ? () {
            _controller.refreshLocation();
          } : null,
          child: Text('刷新位置'),
        ),
      ],
    );
  }
}
```

## 状态说明

### LocationVerificationStatus
- `loading` - 正在获取位置
- `verified` - 位置验证成功（在范围内）
- `notOnSite` - 位置验证成功（不在范围内）
- `error` - 位置获取失败

### LocationVerificationData
位置验证回调数据，包含：
- `status` - 验证状态
- `position` - 当前位置
- `distanceToTarget` - 距离目标的距离
- `isOnSite` - 是否在目标范围内
- `errorMessage` - 错误信息（如果有）

## 样式自定义

```dart
LocationVerificationStyle(
  loadingColor: Colors.blue,      // 加载状态颜色
  verifiedColor: Colors.green,    // 验证通过颜色
  notOnSiteColor: Colors.orange,  // 非实地颜色
  errorColor: Colors.grey,        // 错误状态颜色
  borderRadius: 8.0,              // 边框圆角
  padding: EdgeInsets.all(12),    // 内边距
  titleStyle: TextStyle(...),     // 标题样式
  descriptionStyle: TextStyle(...), // 描述样式
)
```

## 注意事项

1. **权限要求** - 需要位置权限，模块会自动处理权限请求
2. **GPS服务** - 需要设备开启GPS服务
3. **网络要求** - 某些情况下可能需要网络辅助定位
4. **性能考虑** - 模块使用10米距离过滤器，避免频繁更新
5. **错误处理** - 模块会优雅处理各种GPS错误情况

## 集成到现有项目

1. 将模块文件复制到项目中
2. 导入模块：`import 'location_verification/location_verification.dart';`
3. 根据需要使用相应的组件
4. 确保项目已配置位置权限

这个模块设计为完全独立和可复用，可以轻松集成到任何需要位置验证功能的Flutter项目中。
