import 'package:flutter/material.dart';
import '../../models/spot_comment.dart';
import '../../services/service_locator.dart';
import '../snackbar.dart';
import 'comment_input_bar.dart';
import 'comment_list_view.dart';

/// 评论类型枚举
enum CommentType {
  spot, // 钓点评论
  post, // 帖子评论
  activity, // 一起钓鱼活动评论
}

/// 完整的评论系统组件
///
/// 功能：
/// - 集成评论列表和输入栏
/// - 支持多种评论类型
/// - 自动管理回复状态
/// - 支持自定义样式
class CommentSystem extends StatefulWidget {
  /// 目标ID（钓点ID、帖子ID等）
  final String targetId;

  /// 评论类型
  final CommentType type;

  /// 标题（可选）
  final String? title;

  /// 初始评论列表
  final List<SpotComment>? initialComments;

  /// 初始加载状态
  final bool? initialIsLoading;

  /// 评论数量变化回调
  final Function(int)? onCommentCountChanged;

  /// 自定义空状态占位符
  final Widget? emptyPlaceholder;

  /// 自定义加载占位符
  final Widget? loadingPlaceholder;

  /// 输入栏容器装饰
  final Decoration? containerDecoration;

  /// 发送按钮颜色
  final Color? sendButtonColor;

  const CommentSystem({
    super.key,
    required this.targetId,
    required this.type,
    this.title,
    this.initialComments,
    this.initialIsLoading,
    this.onCommentCountChanged,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
    this.containerDecoration,
    this.sendButtonColor,
  });

  @override
  State<CommentSystem> createState() => _CommentSystemState();
}

class _CommentSystemState extends State<CommentSystem> {
  // 评论列表组件的引用
  final GlobalKey<CommentListViewState> _commentListKey =
      GlobalKey<CommentListViewState>();

  // 输入栏组件的引用
  final GlobalKey _inputBarKey = GlobalKey();

  // 回复状态
  SpotComment? _replyingToComment;

  /// 是否处于回复模式
  bool get _isReplying => _replyingToComment != null;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题（可选）
        if (widget.title != null) _buildTitle(),

        // 评论列表
        Expanded(
          child: CommentListView(
            key: _commentListKey,
            targetId: widget.targetId,
            initialComments: widget.initialComments,
            initialIsLoading: widget.initialIsLoading,
            onReply: _startReply,
            onCommentCountChanged: widget.onCommentCountChanged,
            emptyPlaceholder: widget.emptyPlaceholder,
            loadingPlaceholder: widget.loadingPlaceholder,
          ),
        ),

        // 评论输入栏
        CommentInputBar(
          key: _inputBarKey,
          onSubmit: _submitComment,
          replyToComment: _replyingToComment,
          onCancelReply: _cancelReply,
          containerDecoration: widget.containerDecoration,
          sendButtonColor: widget.sendButtonColor,
        ),
      ],
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Icon(_getTypeIcon(), size: 20, color: _getTypeColor()),
          const SizedBox(width: 8),
          Text(
            widget.title!,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          // 评论数量显示
          ValueListenableBuilder<int>(
            valueListenable: _CommentCountNotifier(_commentListKey),
            builder: (context, count, child) {
              if (count == 0) return const SizedBox.shrink();

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTypeColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$count条',
                  style: TextStyle(
                    fontSize: 12,
                    color: _getTypeColor(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 开始回复评论
  void _startReply(SpotComment comment) {
    setState(() {
      _replyingToComment = comment;
    });

    // 聚焦到输入框（通过回调实现）

    debugPrint('🔄 [评论系统] 开始回复评论: ${comment.username} - ${comment.content}');
  }

  /// 取消回复
  void _cancelReply() {
    setState(() {
      _replyingToComment = null;
    });

    debugPrint('❌ [评论系统] 取消回复');
  }

  /// 提交评论
  Future<bool> _submitComment(String content) async {
    try {
      debugPrint('💬 [评论系统] 开始提交${_isReplying ? "回复" : "评论"}');

      // 检查用户是否登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        if (mounted) {
          SnackBarService.showWarning(context, '请先登录后再发表评论');
        }
        return false;
      }

      // 根据评论类型选择相应的服务
      final success = await _submitCommentByType(content);

      if (success) {
        // 刷新评论列表
        await _commentListKey.currentState?.refreshComments();

        // 清除回复状态
        if (_isReplying) {
          setState(() {
            _replyingToComment = null;
          });
        }

        if (mounted) {
          SnackBarService.showSuccess(
            context,
            _isReplying ? '回复发布成功' : '评论发布成功',
          );
        }

        debugPrint('✅ [评论系统] ${_isReplying ? "回复" : "评论"}提交成功');
        return true;
      } else {
        debugPrint('❌ [评论系统] ${_isReplying ? "回复" : "评论"}提交失败');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [评论系统] 评论提交异常: $e');

      if (mounted) {
        SnackBarService.showError(context, '评论发布失败，请稍后重试');
      }

      return false;
    }
  }

  /// 根据评论类型提交评论
  Future<bool> _submitCommentByType(String content) async {
    switch (widget.type) {
      case CommentType.spot:
        return await Services.spotComment.addComment(
          spotId: widget.targetId,
          content: content,
          parentCommentId: _replyingToComment?.id,
          replyToUserId: _replyingToComment?.userId,
          replyToUsername: _replyingToComment?.username,
        );

      case CommentType.post:
        // TODO: 实现帖子评论服务
        debugPrint('⚠️ [评论系统] 帖子评论功能待实现');
        return false;

      case CommentType.activity:
        // TODO: 实现活动评论服务
        debugPrint('⚠️ [评论系统] 活动评论功能待实现');
        return false;
    }
  }

  /// 获取类型图标
  IconData _getTypeIcon() {
    switch (widget.type) {
      case CommentType.spot:
        return Icons.location_on;
      case CommentType.post:
        return Icons.article;
      case CommentType.activity:
        return Icons.group;
    }
  }

  /// 获取类型颜色
  Color _getTypeColor() {
    switch (widget.type) {
      case CommentType.spot:
        return Colors.blue;
      case CommentType.post:
        return Colors.green;
      case CommentType.activity:
        return Colors.orange;
    }
  }

  /// 获取当前评论数量
  int get commentCount => _commentListKey.currentState?.commentCount ?? 0;
}

/// 评论数量通知器
class _CommentCountNotifier extends ValueNotifier<int> {
  final GlobalKey<CommentListViewState> _listKey;

  _CommentCountNotifier(this._listKey) : super(0) {
    // 定期更新评论数量
    _updateCount();
  }

  void _updateCount() {
    final count = _listKey.currentState?.commentCount ?? 0;
    if (value != count) {
      value = count;
    }

    // 延迟更新，避免频繁调用
    Future.delayed(const Duration(milliseconds: 100), _updateCount);
  }
}
