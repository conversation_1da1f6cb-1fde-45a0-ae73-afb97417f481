import 'package:flutter/material.dart';
import '../../models/spot_comment.dart';

/// 通用评论输入栏组件
///
/// 功能：
/// - 支持普通评论和回复评论
/// - 自动管理输入状态
/// - 支持自定义样式
/// - 支持提交回调
class CommentInputBar extends StatefulWidget {
  /// 提交评论回调
  final Future<bool> Function(String content) onSubmit;

  /// 回复的评论（如果是回复模式）
  final SpotComment? replyToComment;

  /// 取消回复回调
  final VoidCallback? onCancelReply;

  /// 输入提示文本
  final String? hintText;

  /// 是否启用输入
  final bool enabled;

  /// 最大行数
  final int? maxLines;

  /// 输入框容器装饰
  final Decoration? containerDecoration;

  /// 发送按钮颜色
  final Color? sendButtonColor;

  const CommentInputBar({
    super.key,
    required this.onSubmit,
    this.replyToComment,
    this.onCancelReply,
    this.hintText,
    this.enabled = true,
    this.maxLines,
    this.containerDecoration,
    this.sendButtonColor,
  });

  @override
  State<CommentInputBar> createState() => _CommentInputBarState();
}

class _CommentInputBarState extends State<CommentInputBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isSubmitting = false;

  /// 是否处于回复模式
  bool get _isReplying => widget.replyToComment != null;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 提交评论
  Future<void> _submitComment() async {
    final content = _controller.text.trim();
    if (content.isEmpty || _isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final success = await widget.onSubmit(content);

      if (success) {
        // 清空输入框
        _controller.clear();
        // 失去焦点
        _focusNode.unfocus();

        debugPrint('✅ [评论输入栏] ${_isReplying ? "回复" : "评论"}提交成功');
      } else {
        debugPrint('❌ [评论输入栏] ${_isReplying ? "回复" : "评论"}提交失败');
      }
    } catch (e) {
      debugPrint('❌ [评论输入栏] ${_isReplying ? "回复" : "评论"}提交异常: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// 取消回复
  void _cancelReply() {
    _controller.clear();
    widget.onCancelReply?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 回复状态指示器
            if (_isReplying) _buildReplyIndicator(),

            // 输入框区域
            _buildInputArea(),
          ],
        ),
      ),
    );
  }

  /// 构建回复状态指示器
  Widget _buildReplyIndicator() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.blue.shade100, width: 1),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.reply, size: 16, color: Colors.blue.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '回复 @${widget.replyToComment!.username}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: _cancelReply,
            child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // 输入框
          Expanded(
            child: Container(
              constraints: const BoxConstraints(minHeight: 36, maxHeight: 100),
              decoration:
                  widget.containerDecoration ??
                  BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLines: widget.maxLines,
                textInputAction: TextInputAction.send,
                enabled: widget.enabled && !_isSubmitting,
                decoration: InputDecoration(
                  hintText: _getHintText(),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty && !_isSubmitting) {
                    _submitComment();
                  }
                },
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 发送按钮
          _buildSendButton(),
        ],
      ),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _controller,
      builder: (context, value, child) {
        final hasText = value.text.trim().isNotEmpty;
        final canSend = hasText && !_isSubmitting && widget.enabled;

        return GestureDetector(
          onTap: canSend ? _submitComment : null,
          child: Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color:
                  canSend
                      ? (widget.sendButtonColor ?? Colors.blue)
                      : Colors.grey.shade300,
              borderRadius: BorderRadius.circular(18),
            ),
            child:
                _isSubmitting
                    ? const Center(
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                    )
                    : Icon(
                      Icons.send,
                      size: 18,
                      color: hasText ? Colors.white : Colors.grey.shade500,
                    ),
          ),
        );
      },
    );
  }

  /// 获取提示文本
  String _getHintText() {
    if (widget.hintText != null) return widget.hintText!;

    if (_isReplying) {
      return '回复 @${widget.replyToComment!.username}...';
    }

    return '写评论...';
  }

  /// 聚焦到输入框
  void focus() {
    _focusNode.requestFocus();
  }

  /// 失去焦点
  void unfocus() {
    _focusNode.unfocus();
  }

  /// 清空输入框
  void clear() {
    _controller.clear();
  }
}
