import 'package:flutter/material.dart';
import '../../models/spot_comment.dart';
import '../../services/service_locator.dart';
import '../tiktok_style_comment.dart';

/// 通用评论列表视图组件
/// 
/// 功能：
/// - 显示评论列表
/// - 支持回复展开/收起
/// - 支持点赞功能
/// - 支持回复回调
/// - 自动管理加载状态
class CommentListView extends StatefulWidget {
  /// 目标ID（钓点ID、帖子ID等）
  final String targetId;
  
  /// 初始评论列表
  final List<SpotComment>? initialComments;
  
  /// 初始加载状态
  final bool? initialIsLoading;
  
  /// 回复回调
  final Function(SpotComment)? onReply;
  
  /// 评论数量变化回调
  final Function(int)? onCommentCountChanged;
  
  /// 刷新回调
  final VoidCallback? onRefresh;
  
  /// 空状态占位符
  final Widget? emptyPlaceholder;
  
  /// 加载占位符
  final Widget? loadingPlaceholder;

  const CommentListView({
    super.key,
    required this.targetId,
    this.initialComments,
    this.initialIsLoading,
    this.onReply,
    this.onCommentCountChanged,
    this.onRefresh,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
  });

  @override
  State<CommentListView> createState() => CommentListViewState();
}

class CommentListViewState extends State<CommentListView> {
  // 回复相关状态
  final Map<String, List<SpotComment>> _repliesCache = {};
  final Map<String, bool> _expandedComments = {};
  final Map<String, bool> _loadingReplies = {};

  // 内部管理的评论列表和加载状态
  List<SpotComment> _comments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 初始化状态
    _comments = widget.initialComments ?? [];
    _isLoading = widget.initialIsLoading ?? false;

    // 如果没有初始评论，则加载评论
    if (_comments.isEmpty && !_isLoading) {
      _loadComments();
    }
  }

  /// 加载评论列表
  Future<void> _loadComments() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('💬 [评论列表] 开始加载评论列表');
      final comments = await Services.spotComment.getSpotComments(widget.targetId);

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoading = false;
        });
        
        // 通知评论数量变化
        widget.onCommentCountChanged?.call(_comments.length);
        
        debugPrint('✅ [评论列表] 评论加载完成，共 ${_comments.length} 条');
      }
    } catch (e) {
      debugPrint('❌ [评论列表] 加载评论失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新评论列表
  Future<void> refreshComments() async {
    await _loadComments();
    widget.onRefresh?.call();
  }

  /// 添加新评论到列表
  void addComment(SpotComment comment) {
    if (mounted) {
      setState(() {
        _comments = [comment, ..._comments];
      });
      
      // 通知评论数量变化
      widget.onCommentCountChanged?.call(_comments.length);
      
      debugPrint('🎯 [评论列表] 本地添加新评论: ${comment.content}');
    }
  }

  /// 处理回复评论
  Future<void> _handleReply(SpotComment comment) async {
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      // TODO: 显示登录提示
      debugPrint('❌ [评论列表] 用户未登录，无法回复');
      return;
    }

    // 调用父组件的回复回调
    widget.onReply?.call(comment);
  }

  /// 加载回复列表
  Future<void> _loadReplies(String commentId) async {
    if (_loadingReplies[commentId] == true) return;

    setState(() {
      _loadingReplies[commentId] = true;
    });

    try {
      final replies = await Services.spotComment.getCommentReplies(commentId);
      setState(() {
        _repliesCache[commentId] = replies;
        _loadingReplies[commentId] = false;
      });
    } catch (e) {
      debugPrint('❌ [评论列表] 加载回复失败: $e');
      setState(() {
        _loadingReplies[commentId] = false;
      });
    }
  }

  /// 切换回复展开状态
  void _toggleReplies(String commentId) {
    final isExpanded = _expandedComments[commentId] ?? false;
    
    if (!isExpanded) {
      // 如果要展开，先加载回复
      _loadReplies(commentId);
    }
    
    setState(() {
      _expandedComments[commentId] = !isExpanded;
    });
  }

  /// 处理点赞变化
  void _handleLikeChanged() {
    // 点赞变化只需要更新本地状态，不需要重新加载整个评论列表
    debugPrint('💖 [评论列表] 点赞状态已更新，无需刷新');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingPlaceholder ?? _buildDefaultLoadingPlaceholder();
    }

    if (_comments.isEmpty) {
      return widget.emptyPlaceholder ?? _buildDefaultEmptyPlaceholder();
    }

    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _comments.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        color: Colors.grey.shade200,
      ),
      itemBuilder: (context, index) {
        final comment = _comments[index];
        final isExpanded = _expandedComments[comment.id] ?? false;
        final replies = _repliesCache[comment.id] ?? [];
        final isLoadingReplies = _loadingReplies[comment.id] ?? false;

        return Column(
          children: [
            GestureDetector(
              onTap: () {
                if (comment.repliesCount > 0) {
                  _toggleReplies(comment.id);
                }
              },
              child: TikTokStyleComment(
                comment: comment,
                onReply: () => _handleReply(comment),
                onLikeChanged: _handleLikeChanged,
                showReplies: isExpanded,
                replies: replies,
                onLoadMoreReplies: replies.length < comment.repliesCount
                    ? () => _loadReplies(comment.id)
                    : null,
              ),
            ),
            
            // 回复加载指示器
            if (isLoadingReplies) _buildReplyLoadingIndicator(),
          ],
        );
      },
    );
  }

  /// 构建默认加载占位符
  Widget _buildDefaultLoadingPlaceholder() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// 构建默认空状态占位符
  Widget _buildDefaultEmptyPlaceholder() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '还没有评论',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '快来发表第一条评论吧！',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建回复加载指示器
  Widget _buildReplyLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 56, vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade400),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '加载回复中...',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取当前评论数量
  int get commentCount => _comments.length;
}
