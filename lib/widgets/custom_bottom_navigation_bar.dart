import 'package:flutter/material.dart';
import 'animated_nav_item.dart';
import '../config/nav_config.dart';

/// 自定义底部导航栏
class CustomBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return RepaintBoundary(
      child: Container(
        height: 70, // 增加高度以适应动画效果
        decoration: BoxDecoration(
          color: colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, -3),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(context, 0),
              _buildNavItem(context, 1),
              _buildAddButton(context),
              _buildNavItem(context, 2),
              _buildNavItem(context, 3),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建普通导航项
  Widget _buildNavItem(BuildContext context, int index) {
    final config = NavConfig.getConfig(index);
    if (config == null) return const SizedBox.shrink();

    return Expanded(
      child: AnimatedNavItem(
        icon: config.icon,
        label: config.label,
        isSelected: currentIndex == index,
        gradientColors: config.gradientColors,
        unselectedIconSize: config.unselectedIconSize,
        selectedIconSize: config.selectedIconSize,
        onTap: () => onTap(index),
      ),
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(-1), // 使用-1表示添加按钮
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFF5722), // 红色
                  Color(0xFFFF9800), // 橙色
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF5722).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(Icons.add, color: Theme.of(context).colorScheme.onPrimary, size: 26),
          ),
        ),
      ),
    );
  }
}
