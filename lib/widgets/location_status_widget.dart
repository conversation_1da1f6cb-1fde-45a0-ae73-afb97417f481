import 'dart:async';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import '../services/location_service.dart';
import '../widgets/snackbar.dart';

/// GPS定位状态
enum LocationStatus {
  idle,      // 空闲状态（灰色）
  loading,   // 定位中（灰绿闪烁）
  success,   // 定位成功（绿色）
  failed,    // 定位失败（灰色）
}

/// 位置状态回调数据
class LocationStatusData {
  final LocationStatus status;
  final LatLng? location;
  final String? errorMessage;

  const LocationStatusData({
    required this.status,
    this.location,
    this.errorMessage,
  });
}

/// GPS定位状态图标组件
/// 
/// 功能：
/// - 显示GPS定位图标
/// - 根据定位状态改变颜色和动画
/// - 点击重新开始定位
/// - 定位中时闪烁动画
class LocationStatusWidget extends StatefulWidget {
  /// 位置更新回调
  final Function(LocationStatusData data)? onLocationUpdate;

  /// 图标大小
  final double size;

  /// 是否自动开始定位
  final bool autoStart;

  /// 定位超时时间（秒）
  final int timeoutSeconds;

  /// 是否显示成功提示
  final bool showSuccessSnackBar;

  const LocationStatusWidget({
    super.key,
    this.onLocationUpdate,
    this.size = 20,
    this.autoStart = true,
    this.timeoutSeconds = 11, // 比LocationService的10秒多1秒缓冲
    this.showSuccessSnackBar = false,
  });

  @override
  State<LocationStatusWidget> createState() => _LocationStatusWidgetState();
}

class _LocationStatusWidgetState extends State<LocationStatusWidget>
    with SingleTickerProviderStateMixin {
  LocationStatus _status = LocationStatus.idle;
  LatLng? _currentLocation;
  String? _errorMessage;
  Timer? _timeoutTimer;
  
  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 创建透明度动画（用于闪烁效果）
    _opacityAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 异步启动定位，不阻塞UI构建
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _startLocationUpdate();
        }
      });
    }
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  /// 开始定位更新
  Future<void> _startLocationUpdate() async {
    if (!mounted) return;

    debugPrint('🔍 [LocationStatusWidget] 开始GPS定位');

    try {
      setState(() {
        _status = LocationStatus.loading;
        _errorMessage = null;
      });

      // 显示开始定位提示
      _showStartLocationSnackBar();

      // 开始闪烁动画
      if (mounted && (_animationController.isCompleted || _animationController.isDismissed)) {
        _animationController.repeat(reverse: true);
      }

      // 设置超时定时器 - 使用LocationService的超时时间+1秒缓冲
      _timeoutTimer?.cancel();
      _timeoutTimer = Timer(const Duration(seconds: 11), () {
        if (mounted && _status == LocationStatus.loading) {
          _handleLocationError('定位超时');
        }
      });

      _notifyLocationUpdate();

      // 使用LocationService获取位置
      final locationService = LocationService();
      final location = await locationService.requestLocationUpdate();

      _timeoutTimer?.cancel();

      // 只有在仍处于loading状态时才更新为成功
      // 这样可以避免超时后又收到成功结果的问题
      if (mounted && _status == LocationStatus.loading) {
        setState(() {
          _status = LocationStatus.success;
          _currentLocation = location;
          _errorMessage = null;
        });

        // 停止闪烁动画
        if (_animationController.isAnimating) {
          _animationController.stop();
        }
        _animationController.value = 1.0;

        _notifyLocationUpdate();
        debugPrint('🔍 [LocationStatusWidget] GPS定位成功: $location');

        // 显示成功提示
        if (widget.showSuccessSnackBar && mounted) {
          _showSuccessSnackBar();
        }
      } else if (mounted && _status != LocationStatus.loading) {
        // 如果状态已经不是loading（比如已经超时），记录这个延迟的成功结果
        debugPrint('🔍 [LocationStatusWidget] 收到延迟的GPS结果: $location (当前状态: $_status)');
      }
    } catch (e) {
      _timeoutTimer?.cancel();
      if (mounted && _status == LocationStatus.loading) {
        _handleLocationError(e.toString());
      }
    }
  }
  
  /// 处理定位错误
  void _handleLocationError(String error) {
    debugPrint('❌ [LocationStatusWidget] GPS定位失败: $error');

    if (!mounted) return;

    try {
      setState(() {
        _status = LocationStatus.failed;
        _currentLocation = null;
        _errorMessage = error;
      });

      // 停止闪烁动画
      if (_animationController.isAnimating) {
        _animationController.stop();
      }
      _animationController.value = 1.0;

      // 显示失败提示
      _showFailureSnackBar();

      _notifyLocationUpdate();
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] 处理错误时发生异常: $e');
    }
  }
  
  /// 通知位置更新
  void _notifyLocationUpdate() {
    if (!mounted) return;

    try {
      if (widget.onLocationUpdate != null) {
        final data = LocationStatusData(
          status: _status,
          location: _currentLocation,
          errorMessage: _errorMessage,
        );
        widget.onLocationUpdate!(data);
      }
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] 通知位置更新时发生异常: $e');
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (_status) {
      case LocationStatus.idle:
        return Colors.grey;
      case LocationStatus.loading:
        return Colors.teal; // 灰绿色
      case LocationStatus.success:
        return Colors.green;
      case LocationStatus.failed:
        return Colors.grey;
    }
  }

  /// 显示开始定位提示
  void _showStartLocationSnackBar() {
    if (!widget.showSuccessSnackBar) return;

    try {
      SnackBarService.showInfo(context, '开始定位');
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] 显示开始定位SnackBar失败: $e');
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar() {
    if (!widget.showSuccessSnackBar) return;

    try {
      SnackBarService.showSuccess(context, 'GPS定位成功!');
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] 显示成功SnackBar失败: $e');
    }
  }

  /// 显示失败提示（带图标）
  void _showFailureSnackBar() {
    if (!widget.showSuccessSnackBar) return;

    try {
      // 使用自定义方法显示带GPS图标的失败提示
      SnackBarService.showCustom(
        context,
        '定位失败，点击 📍 再次尝试',
        backgroundColor: Colors.red,
        icon: Icons.gps_fixed,
      );
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] 显示失败SnackBar失败: $e');
    }
  }

  /// 点击重新定位
  void _onTap() {
    if (!mounted) return;

    debugPrint('🔍 [LocationStatusWidget] 用户点击重新定位');

    // 异步执行定位，避免阻塞UI
    Future.microtask(() {
      if (mounted) {
        _startLocationUpdate();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    try {
      Widget icon = Icon(
        Icons.gps_fixed,
        size: widget.size,
        color: _getStatusColor(),
      );

      // 如果正在定位，应用闪烁动画
      if (_status == LocationStatus.loading && _animationController.isAnimating) {
        icon = AnimatedBuilder(
          animation: _opacityAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Icon(
                Icons.gps_fixed,
                size: widget.size,
                color: _getStatusColor(),
              ),
            );
          },
        );
      }

      return GestureDetector(
        onTap: _onTap,
        child: icon,
      );
    } catch (e) {
      debugPrint('❌ [LocationStatusWidget] build方法发生异常: $e');
      // 发生异常时返回一个简单的静态图标
      return Icon(
        Icons.gps_fixed,
        size: widget.size,
        color: Colors.grey,
      );
    }
  }
}
