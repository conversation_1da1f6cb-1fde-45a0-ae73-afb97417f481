import 'package:flutter/material.dart';
import '../theme/app_theme_manager.dart';

/// 简化的主题选择器
/// 
/// 只提供3个选项：浅色、深色、跟随系统
/// 选择主题模式时自动设置对应的主题类型
class SimpleThemeSelector extends StatefulWidget {
  const SimpleThemeSelector({super.key});

  @override
  State<SimpleThemeSelector> createState() => _SimpleThemeSelectorState();
}

class _SimpleThemeSelectorState extends State<SimpleThemeSelector> {
  late AppThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = AppThemeManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主题设置'),
        elevation: 0,
      ),
      body: ListenableBuilder(
        listenable: _themeManager,
        builder: (context, child) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 说明文字
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          '选择你喜欢的主题模式',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // 主题选项
                _buildThemeOption(
                  title: '浅色主题',
                  subtitle: '明亮清晰，适合白天使用',
                  icon: Icons.light_mode,
                  themeMode: ThemeMode.light,
                ),
                
                const SizedBox(height: 12),
                
                _buildThemeOption(
                  title: '深色主题',
                  subtitle: '护眼舒适，适合夜间使用',
                  icon: Icons.dark_mode,
                  themeMode: ThemeMode.dark,
                ),
                
                const SizedBox(height: 12),
                
                _buildThemeOption(
                  title: '跟随系统',
                  subtitle: '自动根据系统设置切换主题',
                  icon: Icons.brightness_auto,
                  themeMode: ThemeMode.system,
                ),
                
                const Spacer(),
                
                // 预览区域
                _buildPreview(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建主题选项
  Widget _buildThemeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required ThemeMode themeMode,
  }) {
    final isSelected = _themeManager.currentThemeMode == themeMode;
    
    return Card(
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () => _setThemeMode(themeMode),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected 
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Row(
            children: [
              // 图标
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: isSelected 
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // 文字
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 选中状态
              Icon(
                isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建预览区域
  Widget _buildPreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '预览效果',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Icon(
                    Icons.person,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '用户名称',
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      Text(
                        '这是预览文字效果',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                FilledButton(
                  onPressed: () {},
                  child: const Text('按钮'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 设置主题模式
  void _setThemeMode(ThemeMode mode) {
    _themeManager.setThemeMode(mode);
    
    // 显示反馈
    String message;
    switch (mode) {
      case ThemeMode.light:
        message = '已切换到浅色主题';
        break;
      case ThemeMode.dark:
        message = '已切换到深色主题';
        break;
      case ThemeMode.system:
        message = '已设置为跟随系统';
        break;
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}