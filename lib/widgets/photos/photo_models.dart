import '../../models/spot_photo.dart';

/// 照片显示模式枚举
enum PhotoDisplayMode {
  carousel,    // 轮播模式
  grid,        // 网格模式
  list,        // 列表模式
  compact,     // 紧凑模式（水平滚动）
}

/// 照片类型枚举
enum PhotoType {
  normal,      // 普通照片
  panorama,    // 全景照片
  avatar,      // 头像照片
}

/// 通用照片项模型
/// 
/// 用于统一不同来源的照片数据
class PhotoItem {
  /// 唯一标识符
  final String id;
  
  /// 照片URL
  final String url;
  
  /// 缩略图URL
  final String? thumbnailUrl;
  
  /// 照片描述
  final String? description;
  
  /// 照片类型
  final PhotoType type;
  
  /// 排序顺序
  final int sortOrder;
  
  /// 文件大小（字节）
  final int? fileSize;
  
  /// 创建时间
  final DateTime? created;

  const PhotoItem({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    this.description,
    this.type = PhotoType.normal,
    this.sortOrder = 0,
    this.fileSize,
    this.created,
  });

  /// 从钓点照片创建
  factory PhotoItem.fromSpotPhoto(SpotPhoto spotPhoto) {
    return PhotoItem(
      id: spotPhoto.id,
      url: spotPhoto.url,
      thumbnailUrl: spotPhoto.thumbnailUrl,
      description: spotPhoto.description,
      type: spotPhoto.type == 'panorama' ? PhotoType.panorama : PhotoType.normal,
      sortOrder: spotPhoto.sortOrder,
      fileSize: spotPhoto.fileSize,
      created: spotPhoto.created,
    );
  }

  /// 从URL创建简单照片项
  factory PhotoItem.fromUrl({
    required String id,
    required String url,
    String? thumbnailUrl,
    String? description,
    PhotoType type = PhotoType.normal,
  }) {
    return PhotoItem(
      id: id,
      url: url,
      thumbnailUrl: thumbnailUrl,
      description: description,
      type: type,
    );
  }

  /// 获取显示用的图片URL（优先使用缩略图）
  String get displayUrl => thumbnailUrl ?? url;

  /// 获取原图URL
  String get originalUrl => url;

  /// 是否为全景照片
  bool get isPanorama => type == PhotoType.panorama;

  /// 是否为头像
  bool get isAvatar => type == PhotoType.avatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PhotoItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PhotoItem{id: $id, url: $url, type: $type}';
  }
}

/// 照片画廊配置
class PhotoGalleryConfig {
  /// 显示模式
  final PhotoDisplayMode mode;
  
  /// 是否启用查看器
  final bool enableViewer;
  
  /// 是否启用无限滚动（轮播模式）
  final bool enableInfiniteScroll;
  
  /// 是否自动播放（轮播模式）
  final bool autoPlay;
  
  /// 自动播放间隔（秒）
  final int autoPlayInterval;
  
  /// 网格列数（网格模式）
  final int gridCrossAxisCount;
  
  /// 网格宽高比（网格模式）
  final double gridChildAspectRatio;
  
  /// 最大显示数量（0表示无限制）
  final int maxDisplayCount;
  
  /// 轮播高度（轮播模式）
  final double? carouselHeight;
  
  /// 紧凑模式高度
  final double compactHeight;

  const PhotoGalleryConfig({
    this.mode = PhotoDisplayMode.carousel,
    this.enableViewer = true,
    this.enableInfiniteScroll = true,
    this.autoPlay = false,
    this.autoPlayInterval = 3,
    this.gridCrossAxisCount = 3,
    this.gridChildAspectRatio = 1.0,
    this.maxDisplayCount = 0,
    this.carouselHeight,
    this.compactHeight = 120.0,
  });

  /// 创建轮播配置
  factory PhotoGalleryConfig.carousel({
    double? height,
    bool enableViewer = true,
    bool enableInfiniteScroll = true,
    bool autoPlay = false,
  }) {
    return PhotoGalleryConfig(
      mode: PhotoDisplayMode.carousel,
      carouselHeight: height,
      enableViewer: enableViewer,
      enableInfiniteScroll: enableInfiniteScroll,
      autoPlay: autoPlay,
    );
  }

  /// 创建网格配置
  factory PhotoGalleryConfig.grid({
    int crossAxisCount = 3,
    double childAspectRatio = 1.0,
    int maxDisplayCount = 0,
    bool enableViewer = true,
  }) {
    return PhotoGalleryConfig(
      mode: PhotoDisplayMode.grid,
      gridCrossAxisCount: crossAxisCount,
      gridChildAspectRatio: childAspectRatio,
      maxDisplayCount: maxDisplayCount,
      enableViewer: enableViewer,
    );
  }

  /// 创建紧凑配置
  factory PhotoGalleryConfig.compact({
    double height = 120.0,
    bool enableViewer = true,
  }) {
    return PhotoGalleryConfig(
      mode: PhotoDisplayMode.compact,
      compactHeight: height,
      enableViewer: enableViewer,
    );
  }

  /// 创建列表配置
  factory PhotoGalleryConfig.list({
    bool enableViewer = true,
  }) {
    return PhotoGalleryConfig(
      mode: PhotoDisplayMode.list,
      enableViewer: enableViewer,
    );
  }
}
