import 'package:flutter/material.dart';
import 'photo_models.dart';
import 'photo_carousel.dart';
import 'photo_grid.dart';
import 'photo_viewer.dart';

/// 照片画廊组件
///
/// 功能：
/// - 统一的照片展示接口
/// - 支持多种显示模式
/// - 自动管理查看器
/// - 支持自定义配置
class PhotoGallery extends StatefulWidget {
  /// 照片列表
  final List<PhotoItem> photos;

  /// 配置
  final PhotoGalleryConfig config;

  /// 照片点击回调（如果不启用查看器）
  final Function(int index)? onPhotoTap;

  /// "查看更多"回调
  final VoidCallback? onViewMore;

  /// 页面变化回调（轮播模式）
  final Function(int index)? onPageChanged;

  /// 无照片占位符
  final Widget? emptyPlaceholder;

  /// 加载占位符
  final Widget? loadingPlaceholder;

  /// 错误占位符
  final Widget? errorPlaceholder;

  /// 是否正在加载
  final bool isLoading;

  const PhotoGallery({
    super.key,
    required this.photos,
    this.config = const PhotoGalleryConfig(),
    this.onPhotoTap,
    this.onViewMore,
    this.onPageChanged,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
    this.errorPlaceholder,
    this.isLoading = false,
  });

  @override
  State<PhotoGallery> createState() => _PhotoGalleryState();
}

class _PhotoGalleryState extends State<PhotoGallery> {
  /// 处理照片点击
  void _handlePhotoTap(int index) {
    if (widget.config.enableViewer) {
      // 显示照片查看器
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => PhotoViewer(
                photos: widget.photos,
                initialIndex: index,
                onPageChanged: widget.onPageChanged,
              ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 300),
          barrierColor: Colors.black,
          opaque: false,
        ),
      );
    } else {
      // 调用自定义回调
      widget.onPhotoTap?.call(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 显示加载状态
    if (widget.isLoading) {
      return widget.loadingPlaceholder ?? _buildDefaultLoadingPlaceholder();
    }

    // 根据配置选择显示模式
    switch (widget.config.mode) {
      case PhotoDisplayMode.carousel:
        return _buildCarouselMode();

      case PhotoDisplayMode.compact:
        return _buildCompactMode();

      case PhotoDisplayMode.grid:
        return _buildGridMode();

      case PhotoDisplayMode.list:
        return _buildListMode();
    }
  }

  /// 构建轮播模式
  Widget _buildCarouselMode() {
    return PhotoCarousel(
      photos: widget.photos,
      config: widget.config,
      onPhotoTap: _handlePhotoTap,
      onPageChanged: widget.onPageChanged,
      emptyPlaceholder: widget.emptyPlaceholder,
      loadingPlaceholder: widget.loadingPlaceholder,
      errorPlaceholder: widget.errorPlaceholder,
    );
  }

  /// 构建紧凑模式
  Widget _buildCompactMode() {
    return PhotoCarousel(
      photos: widget.photos,
      config: widget.config,
      onPhotoTap: _handlePhotoTap,
      onPageChanged: widget.onPageChanged,
      emptyPlaceholder: widget.emptyPlaceholder,
      loadingPlaceholder: widget.loadingPlaceholder,
      errorPlaceholder: widget.errorPlaceholder,
    );
  }

  /// 构建网格模式
  Widget _buildGridMode() {
    return PhotoGrid(
      photos: widget.photos,
      config: widget.config,
      onPhotoTap: _handlePhotoTap,
      onViewMore: widget.onViewMore,
      emptyPlaceholder: widget.emptyPlaceholder,
      loadingPlaceholder: widget.loadingPlaceholder,
      errorPlaceholder: widget.errorPlaceholder,
    );
  }

  /// 构建列表模式
  Widget _buildListMode() {
    if (widget.photos.isEmpty) {
      return widget.emptyPlaceholder ?? _buildDefaultEmptyPlaceholder();
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.photos.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final photo = widget.photos[index];
        return GestureDetector(
          onTap: () => _handlePhotoTap(index),
          child: Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // 照片
                  SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: Image.network(
                      photo.displayUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return widget.errorPlaceholder ??
                            Container(
                              color: Colors.grey.shade200,
                              child: const Center(
                                child: Icon(Icons.broken_image, size: 48),
                              ),
                            );
                      },
                    ),
                  ),

                  // 描述（如果有）
                  if (photo.description != null &&
                      photo.description!.isNotEmpty)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.7),
                            ],
                          ),
                        ),
                        child: Text(
                          photo.description!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建默认加载占位符
  Widget _buildDefaultLoadingPlaceholder() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  /// 构建默认空占位符
  Widget _buildDefaultEmptyPlaceholder() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              '暂无照片',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }
}

/// 便捷的静态方法
extension PhotoGalleryExtensions on PhotoGallery {
  /// 创建轮播画廊
  static PhotoGallery carousel({
    required List<PhotoItem> photos,
    double? height,
    bool enableViewer = true,
    bool autoPlay = false,
    Function(int index)? onPageChanged,
    Widget? emptyPlaceholder,
  }) {
    return PhotoGallery(
      photos: photos,
      config: PhotoGalleryConfig.carousel(
        height: height,
        enableViewer: enableViewer,
        autoPlay: autoPlay,
      ),
      onPageChanged: onPageChanged,
      emptyPlaceholder: emptyPlaceholder,
    );
  }

  /// 创建网格画廊
  static PhotoGallery grid({
    required List<PhotoItem> photos,
    int crossAxisCount = 3,
    int maxDisplayCount = 6,
    bool enableViewer = true,
    VoidCallback? onViewMore,
    Widget? emptyPlaceholder,
  }) {
    return PhotoGallery(
      photos: photos,
      config: PhotoGalleryConfig.grid(
        crossAxisCount: crossAxisCount,
        maxDisplayCount: maxDisplayCount,
        enableViewer: enableViewer,
      ),
      onViewMore: onViewMore,
      emptyPlaceholder: emptyPlaceholder,
    );
  }

  /// 创建紧凑画廊
  static PhotoGallery compact({
    required List<PhotoItem> photos,
    double height = 120.0,
    bool enableViewer = true,
    Widget? emptyPlaceholder,
  }) {
    return PhotoGallery(
      photos: photos,
      config: PhotoGalleryConfig.compact(
        height: height,
        enableViewer: enableViewer,
      ),
      emptyPlaceholder: emptyPlaceholder,
    );
  }
}
