import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../../services/unified_image_service.dart';
import 'photo_models.dart';

/// 照片轮播组件
///
/// 功能：
/// - 支持轮播和紧凑模式
/// - 支持点击查看大图
/// - 支持自动播放
/// - 支持页面指示器
class PhotoCarousel extends StatefulWidget {
  /// 照片列表
  final List<PhotoItem> photos;

  /// 配置
  final PhotoGalleryConfig config;

  /// 照片点击回调
  final Function(int index)? onPhotoTap;

  /// 页面变化回调
  final Function(int index)? onPageChanged;

  /// 无照片占位符
  final Widget? emptyPlaceholder;

  /// 加载占位符
  final Widget? loadingPlaceholder;

  /// 错误占位符
  final Widget? errorPlaceholder;

  const PhotoCarousel({
    super.key,
    required this.photos,
    required this.config,
    this.onPhotoTap,
    this.onPageChanged,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
    this.errorPlaceholder,
  });

  @override
  State<PhotoCarousel> createState() => _PhotoCarouselState();
}

class _PhotoCarouselState extends State<PhotoCarousel> {
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  final UnifiedImageService _imageService = UnifiedImageService();
  int _currentIndex = 0;

  /// 是否为紧凑模式
  bool get _isCompactMode => widget.config.mode == PhotoDisplayMode.compact;

  @override
  Widget build(BuildContext context) {
    if (widget.photos.isEmpty) {
      return widget.emptyPlaceholder ?? _buildDefaultEmptyPlaceholder();
    }

    if (_isCompactMode) {
      return _buildCompactCarousel();
    } else {
      return _buildNormalCarousel();
    }
  }

  /// 构建正常轮播
  Widget _buildNormalCarousel() {
    return Column(
      children: [
        // 轮播主体
        Expanded(
          child: CarouselSlider.builder(
            carouselController: _carouselController,
            itemCount: widget.photos.length,
            itemBuilder: (context, index, realIndex) {
              return _buildPhotoItem(widget.photos[index], index);
            },
            options: CarouselOptions(
              height: widget.config.carouselHeight,
              viewportFraction: 1.0,
              enableInfiniteScroll:
                  widget.config.enableInfiniteScroll &&
                  widget.photos.length > 1,
              autoPlay: widget.config.autoPlay && widget.photos.length > 1,
              autoPlayInterval: Duration(
                seconds: widget.config.autoPlayInterval,
              ),
              onPageChanged: (index, reason) {
                setState(() {
                  _currentIndex = index;
                });
                widget.onPageChanged?.call(index);
              },
            ),
          ),
        ),

        // 页面指示器
        if (widget.photos.length > 1) _buildPageIndicator(),
      ],
    );
  }

  /// 构建紧凑轮播（水平滚动）
  Widget _buildCompactCarousel() {
    return SizedBox(
      height: widget.config.compactHeight,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.photos.length,
        itemBuilder: (context, index) {
          final photo = widget.photos[index];

          return Container(
            width: widget.config.compactHeight, // 正方形
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 4,
              right: index == widget.photos.length - 1 ? 16 : 4,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildPhotoItem(photo, index),
            ),
          );
        },
      ),
    );
  }

  /// 构建照片项
  Widget _buildPhotoItem(PhotoItem photo, int index) {
    return GestureDetector(
      onTap: () => widget.onPhotoTap?.call(index),
      child: Stack(
        children: [
          // 照片
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: _buildSignedImage(photo),
          ),

          // 全景照片标识
          if (photo.isPanorama && !_isCompactMode) _buildPanoramaIndicator(),

          // 紧凑模式下的照片数量指示器
          if (_isCompactMode && index == 0 && widget.photos.length > 1)
            _buildCompactCountIndicator(),
        ],
      ),
    );
  }

  /// 构建签名图片
  Widget _buildSignedImage(PhotoItem photo) {
    return _imageService.buildCachedSignedImage(
      originalUrl: photo.displayUrl,
      fit: BoxFit.cover,
      placeholder:
          widget.loadingPlaceholder ??
          Container(
            color: Colors.grey.shade200,
            child: const Center(child: CircularProgressIndicator()),
          ),
      errorWidget:
          widget.errorPlaceholder ??
          Container(
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text('图片加载失败', style: TextStyle(color: Colors.grey.shade500)),
                ],
              ),
            ),
          ),
      isAvatar: photo.isAvatar,
    );
  }

  /// 构建全景照片指示器
  Widget _buildPanoramaIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          '360°',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建紧凑模式数量指示器
  Widget _buildCompactCountIndicator() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          '${widget.photos.length}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_currentIndex + 1}/${widget.photos.length}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建默认空占位符
  Widget _buildDefaultEmptyPlaceholder() {
    if (_isCompactMode) {
      return Container(
        height: widget.config.compactHeight,
        color: Colors.grey.shade100,
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.photo_camera_outlined,
                size: 24,
                color: Colors.grey.shade400,
              ),
              const SizedBox(width: 8),
              Text(
                '暂无照片',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              '暂无照片',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到指定页面
  void jumpToPage(int index) {
    if (index >= 0 && index < widget.photos.length) {
      _carouselController.animateToPage(index);
    }
  }

  /// 获取当前页面索引
  int get currentIndex => _currentIndex;
}
