import 'package:flutter/material.dart';
import '../../services/unified_image_service.dart';
import 'photo_models.dart';

/// 照片网格组件
///
/// 功能：
/// - 网格展示照片
/// - 支持最大显示数量限制
/// - 支持"更多"按钮
/// - 支持点击查看大图
class PhotoGrid extends StatelessWidget {
  /// 照片列表
  final List<PhotoItem> photos;

  /// 配置
  final PhotoGalleryConfig config;

  /// 照片点击回调
  final Function(int index)? onPhotoTap;

  /// "查看更多"回调
  final VoidCallback? onViewMore;

  /// 无照片占位符
  final Widget? emptyPlaceholder;

  /// 加载占位符
  final Widget? loadingPlaceholder;

  /// 错误占位符
  final Widget? errorPlaceholder;

  PhotoGrid({
    super.key,
    required this.photos,
    required this.config,
    this.onPhotoTap,
    this.onViewMore,
    this.emptyPlaceholder,
    this.loadingPlaceholder,
    this.errorPlaceholder,
  });

  final UnifiedImageService _imageService = UnifiedImageService();

  @override
  Widget build(BuildContext context) {
    if (photos.isEmpty) {
      return emptyPlaceholder ?? _buildDefaultEmptyPlaceholder();
    }

    // 计算实际显示的照片数量
    final maxCount =
        config.maxDisplayCount > 0 ? config.maxDisplayCount : photos.length;
    final displayCount = photos.length > maxCount ? maxCount : photos.length;
    final hasMore = photos.length > maxCount;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: config.gridCrossAxisCount,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: config.gridChildAspectRatio,
      ),
      itemCount: displayCount,
      itemBuilder: (context, index) {
        // 如果是最后一个位置且有更多照片，显示"更多"按钮
        if (hasMore && index == displayCount - 1) {
          return _buildMoreButton(photos.length - displayCount + 1);
        }

        return _buildPhotoThumbnail(photos[index], index);
      },
    );
  }

  /// 构建照片缩略图
  Widget _buildPhotoThumbnail(PhotoItem photo, int index) {
    return GestureDetector(
      onTap: () => onPhotoTap?.call(index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 照片
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: _buildSignedImage(photo),
              ),

              // 全景照片标识
              if (photo.isPanorama) _buildPanoramaIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建签名图片
  Widget _buildSignedImage(PhotoItem photo) {
    return _imageService.buildCachedSignedImage(
      originalUrl: photo.displayUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
      placeholder:
          loadingPlaceholder ??
          Container(
            color: Colors.grey.shade200,
            child: const Center(child: CircularProgressIndicator()),
          ),
      errorWidget:
          errorPlaceholder ??
          Container(
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    size: 24,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '加载失败',
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
                  ),
                ],
              ),
            ),
          ),
      isAvatar: photo.isAvatar,
    );
  }

  /// 构建全景照片指示器
  Widget _buildPanoramaIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          '360°',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建"更多"按钮
  Widget _buildMoreButton(int remainingCount) {
    return GestureDetector(
      onTap: onViewMore,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.more_horiz, color: Colors.white, size: 24),
              const SizedBox(height: 4),
              Text(
                '+$remainingCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建默认空占位符
  Widget _buildDefaultEmptyPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 32,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无照片',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }
}
