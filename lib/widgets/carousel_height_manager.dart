import 'package:flutter/material.dart';

/// 轮播图高度档位枚举
enum CarouselHeightLevel {
  full, // 全高度 - 详情页、照片页
  medium, // 50%高度 - 评论页
  minimal, // 最小高度 - 键盘弹出时
}

/// 页面类型枚举
enum PageType {
  details, // 详情页
  photos, // 照片页
  comments, // 评论页
}

/// 轮播图高度管理器
///
/// 根据页面状态和键盘状态自动管理轮播图高度
class CarouselHeightManager extends ChangeNotifier {
  static const double _fullHeight = 300.0;
  static const double _mediumHeight = 150.0;
  static const double _minimalHeight = 80.0;

  PageType _currentPageType = PageType.details;
  bool _isKeyboardVisible = false;
  CarouselHeightLevel _currentLevel = CarouselHeightLevel.full;

  /// 当前页面类型
  PageType get currentPageType => _currentPageType;

  /// 键盘是否可见
  bool get isKeyboardVisible => _isKeyboardVisible;

  /// 当前高度档位
  CarouselHeightLevel get currentLevel => _currentLevel;

  /// 当前高度值
  double get currentHeight {
    switch (_currentLevel) {
      case CarouselHeightLevel.full:
        return _fullHeight;
      case CarouselHeightLevel.medium:
        return _mediumHeight;
      case CarouselHeightLevel.minimal:
        return _minimalHeight;
    }
  }

  /// 更新页面类型
  void updatePageType(PageType pageType) {
    debugPrint('🎯 [轮播图高度] 页面类型变化: $_currentPageType -> $pageType');
    if (_currentPageType != pageType) {
      _currentPageType = pageType;
      _updateHeightLevel();
    } else {
      debugPrint('🎯 [轮播图高度] 页面类型未变化，跳过更新');
    }
  }

  /// 更新键盘状态
  void updateKeyboardState(bool isVisible) {
    debugPrint('⌨️ [轮播图高度] 键盘状态变化: $_isKeyboardVisible -> $isVisible');
    if (_isKeyboardVisible != isVisible) {
      _isKeyboardVisible = isVisible;
      _updateHeightLevel();
    } else {
      debugPrint('⌨️ [轮播图高度] 键盘状态未变化，跳过更新');
    }
  }

  /// 根据当前状态更新高度档位
  void _updateHeightLevel() {
    CarouselHeightLevel newLevel;

    debugPrint('📏 [轮播图高度] 开始计算新高度档位');
    debugPrint('📏 [轮播图高度] 当前状态: 页面=$_currentPageType, 键盘=$_isKeyboardVisible');

    if (_isKeyboardVisible) {
      // 键盘弹出时使用最小高度
      newLevel = CarouselHeightLevel.minimal;
      debugPrint('📏 [轮播图高度] 键盘弹出，使用最小高度');
    } else {
      // 键盘收起时根据页面类型决定高度
      switch (_currentPageType) {
        case PageType.details:
        case PageType.photos:
          newLevel = CarouselHeightLevel.full;
          debugPrint('📏 [轮播图高度] 详情/照片页，使用全高度');
          break;
        case PageType.comments:
          newLevel = CarouselHeightLevel.medium;
          debugPrint('📏 [轮播图高度] 评论页，使用中等高度');
          break;
      }
    }

    debugPrint('📏 [轮播图高度] 高度档位变化: $_currentLevel -> $newLevel');
    debugPrint(
      '📏 [轮播图高度] 高度值变化: $currentHeight -> ${getHeightForLevel(newLevel)}',
    );

    if (_currentLevel != newLevel) {
      _currentLevel = newLevel;
      debugPrint('✅ [轮播图高度] 高度档位已更新，通知监听器');
      notifyListeners();
    } else {
      debugPrint('📏 [轮播图高度] 高度档位未变化，跳过通知');
    }
  }

  /// 手动设置高度档位
  void setHeightLevel(CarouselHeightLevel level) {
    if (_currentLevel != level) {
      _currentLevel = level;
      notifyListeners();
    }
  }

  /// 获取指定档位的高度值
  static double getHeightForLevel(CarouselHeightLevel level) {
    switch (level) {
      case CarouselHeightLevel.full:
        return _fullHeight;
      case CarouselHeightLevel.medium:
        return _mediumHeight;
      case CarouselHeightLevel.minimal:
        return _minimalHeight;
    }
  }

  /// 创建带动画的高度组件
  Widget buildAnimatedContainer({
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: curve,
      height: currentHeight,
      child: child,
    );
  }
}

/// 轮播图高度管理器的Provider包装器
class CarouselHeightProvider extends InheritedNotifier<CarouselHeightManager> {
  const CarouselHeightProvider({
    super.key,
    required CarouselHeightManager manager,
    required super.child,
  }) : super(notifier: manager);

  static CarouselHeightManager? maybeOf(BuildContext context) {
    return context
        .dependOnInheritedWidgetOfExactType<CarouselHeightProvider>()
        ?.notifier;
  }

  static CarouselHeightManager of(BuildContext context, {bool listen = true}) {
    if (listen) {
      return context
          .dependOnInheritedWidgetOfExactType<CarouselHeightProvider>()!
          .notifier!;
    } else {
      return (context
                  .getElementForInheritedWidgetOfExactType<
                    CarouselHeightProvider
                  >()
                  ?.widget
              as CarouselHeightProvider)
          .notifier!;
    }
  }
}
