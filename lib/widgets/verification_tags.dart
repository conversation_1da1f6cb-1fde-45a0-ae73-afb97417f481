import 'package:flutter/material.dart';

/// 实地验证标签组件
class OnSiteTag extends StatelessWidget {
  final bool isVerified;
  final VoidCallback? onTap;
  final double size;

  const OnSiteTag({
    super.key,
    required this.isVerified,
    this.onTap,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isVerified
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
          border: Border.all(
            color: isVerified ? Colors.green : Colors.grey,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isVerified ? Icons.location_on : Icons.location_off,
              size: size,
              color: isVerified ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 4),
            Text(
              '实地',
              style: TextStyle(
                fontSize: size * 0.6,
                color: isVerified ? Colors.green : Colors.grey,
                fontWeight: isVerified ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 实拍验证标签组件
class CameraShotTag extends StatelessWidget {
  final bool isVerified;
  final VoidCallback? onTap;
  final double size;

  const CameraShotTag({
    super.key,
    required this.isVerified,
    this.onTap,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isVerified
                  ? Colors.blue.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
          border: Border.all(
            color: isVerified ? Colors.blue : Colors.grey,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isVerified ? Icons.camera_alt : Icons.camera_alt_outlined,
              size: size,
              color: isVerified ? Colors.blue : Colors.grey,
            ),
            const SizedBox(width: 4),
            Text(
              '实拍',
              style: TextStyle(
                fontSize: size * 0.6,
                color: isVerified ? Colors.blue : Colors.grey,
                fontWeight: isVerified ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 组合验证标签组件
class VerificationTags extends StatelessWidget {
  final bool isOnSite;
  final bool isCameraShot;
  final VoidCallback? onOnSiteTap;
  final VoidCallback? onCameraShotTap;
  final double size;
  final MainAxisAlignment alignment;

  const VerificationTags({
    super.key,
    required this.isOnSite,
    required this.isCameraShot,
    this.onOnSiteTap,
    this.onCameraShotTap,
    this.size = 24.0,
    this.alignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: alignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        OnSiteTag(isVerified: isOnSite, onTap: onOnSiteTap, size: size),
        const SizedBox(width: 8),
        CameraShotTag(
          isVerified: isCameraShot,
          onTap: onCameraShotTap,
          size: size,
        ),
      ],
    );
  }
}
