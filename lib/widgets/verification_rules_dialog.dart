import 'package:flutter/material.dart';

/// 验证规则说明弹窗
class VerificationRulesDialog extends StatelessWidget {
  final String type; // 'onsite' 或 'camera'

  const VerificationRulesDialog({
    super.key,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            type == 'onsite' ? Icons.location_on : Icons.camera_alt,
            color: type == 'onsite' ? Colors.green : Colors.blue,
          ),
          const SizedBox(width: 8),
          Text(
            type == 'onsite' ? '实地发布规则' : '实拍照片规则',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type == 'onsite') ..._buildOnSiteRules(),
            if (type == 'camera') ..._buildCameraRules(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('知道了'),
        ),
      ],
    );
  }

  List<Widget> _buildOnSiteRules() {
    return [
      const Text(
        '什么是实地发布？',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      const Text(
        '实地发布是指您在钓点现场（50米范围内）发布钓点信息。',
        style: TextStyle(fontSize: 14),
      ),
      const SizedBox(height: 16),
      const Text(
        '认证条件：',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      _buildRuleItem('📍', '发布位置与钓点坐标距离≤50米'),
      _buildRuleItem('🕐', '系统自动检测GPS位置进行验证'),
      _buildRuleItem('✅', '验证通过后显示绿色"实地"标签'),
      const SizedBox(height: 16),
      const Text(
        '实地发布的优势：',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      _buildRuleItem('🏆', '提高钓点信息的可信度'),
      _buildRuleItem('⭐', '获得更多用户信任'),
      _buildRuleItem('🎯', '确保位置信息准确性'),
    ];
  }

  List<Widget> _buildCameraRules() {
    return [
      const Text(
        '什么是实拍照片？',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      const Text(
        '实拍照片是指使用相机直接拍摄的照片，而非从相册中选择的照片。',
        style: TextStyle(fontSize: 14),
      ),
      const SizedBox(height: 16),
      const Text(
        '认证条件：',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      _buildRuleItem('📷', '所有照片都必须通过相机拍摄'),
      _buildRuleItem('🚫', '不能从相册中选择已有照片'),
      _buildRuleItem('✅', '验证通过后显示蓝色"实拍"标签'),
      const SizedBox(height: 16),
      const Text(
        '实拍照片的优势：',
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      const SizedBox(height: 8),
      _buildRuleItem('🏆', '证明照片的真实性和时效性'),
      _buildRuleItem('⭐', '提高钓点信息的可信度'),
      _buildRuleItem('🎯', '避免使用网络图片或过期照片'),
    ];
  }

  Widget _buildRuleItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示实地发布规则弹窗
void showOnSiteRulesDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const VerificationRulesDialog(type: 'onsite'),
  );
}

/// 显示实拍照片规则弹窗
void showCameraRulesDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const VerificationRulesDialog(type: 'camera'),
  );
}
