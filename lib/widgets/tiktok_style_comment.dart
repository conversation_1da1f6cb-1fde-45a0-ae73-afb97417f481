import 'package:flutter/material.dart';
import '../models/spot_comment.dart';
import '../services/service_locator.dart';
import 'snackbar.dart';

/// 抖音风格的评论组件
class TikTokStyleComment extends StatefulWidget {
  final SpotComment comment;
  final VoidCallback? onReply;
  final VoidCallback? onLikeChanged;
  final bool showReplies;
  final List<SpotComment> replies;
  final VoidCallback? onLoadMoreReplies;
  final bool isReply;

  const TikTokStyleComment({
    super.key,
    required this.comment,
    this.onReply,
    this.onLikeChanged,
    this.showReplies = false,
    this.replies = const [],
    this.onLoadMoreReplies,
    this.isReply = false,
  });

  @override
  State<TikTokStyleComment> createState() => _TikTokStyleCommentState();
}

class _TikTokStyleCommentState extends State<TikTokStyleComment> with SingleTickerProviderStateMixin {
  late bool _isLiked;
  late int _likesCount;
  late AnimationController _likeAnimationController;
  late Animation<double> _likeAnimation;
  bool _isLiking = false;

  @override
  void initState() {
    super.initState();
    _isLiked = widget.comment.isLikedByCurrentUser;
    _likesCount = widget.comment.likesCount;
    
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _likeAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _likeAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    super.dispose();
  }

  /// 处理点赞
  Future<void> _handleLike() async {
    debugPrint('💖 [点赞] 开始处理点赞: ${widget.comment.id}');

    if (_isLiking) {
      debugPrint('💖 [点赞] 正在处理中，跳过');
      return;
    }

    setState(() {
      _isLiking = true;
      _isLiked = !_isLiked;
      _likesCount += _isLiked ? 1 : -1;
    });

    debugPrint('💖 [点赞] 本地状态更新: isLiked=$_isLiked, count=$_likesCount');

    // 播放动画
    if (_isLiked) {
      _likeAnimationController.forward().then((_) {
        _likeAnimationController.reverse();
      });
    }

    try {
      debugPrint('💖 [点赞] 调用服务接口');
      final success = await Services.spotComment.toggleCommentLike(widget.comment.id);
      debugPrint('💖 [点赞] 服务接口返回: $success');

      if (!success) {
        // 如果失败，恢复状态
        debugPrint('💖 [点赞] 操作失败，恢复状态');
        setState(() {
          _isLiked = !_isLiked;
          _likesCount += _isLiked ? 1 : -1;
        });
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请重试');
        }
      } else {
        debugPrint('💖 [点赞] 操作成功');
        widget.onLikeChanged?.call();
      }
    } catch (e) {
      // 如果失败，恢复状态
      debugPrint('💖 [点赞] 操作异常: $e');
      setState(() {
        _isLiked = !_isLiked;
        _likesCount += _isLiked ? 1 : -1;
      });
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请重试');
      }
    } finally {
      setState(() {
        _isLiking = false;
      });
      debugPrint('💖 [点赞] 处理完成');
    }
  }

  /// 格式化时间显示
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}-${time.day}';
    }
  }

  /// 格式化点赞数显示
  String _formatLikesCount(int count) {
    if (count == 0) return '';
    if (count < 1000) return count.toString();
    if (count < 10000) return '${(count / 1000).toStringAsFixed(1)}k';
    return '${(count / 10000).toStringAsFixed(1)}w';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 12,
      ),
      margin: EdgeInsets.only(left: widget.isReply ? 40 : 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主评论内容
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户头像
              CircleAvatar(
                radius: widget.isReply ? 16 : 20,
                backgroundColor: Colors.grey.shade300,
                child: Icon(
                  Icons.person,
                  size: widget.isReply ? 16 : 20,
                  color: Colors.grey.shade600,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 评论内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户名和时间
                    Row(
                      children: [
                        Text(
                          widget.comment.username,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: widget.isReply ? 13 : 14,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatTime(widget.comment.created),
                          style: TextStyle(
                            fontSize: widget.isReply ? 11 : 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // 评论内容
                    Text(
                      widget.comment.replyToUsername != null 
                          ? '@${widget.comment.replyToUsername} ${widget.comment.content}'
                          : widget.comment.content,
                      style: TextStyle(
                        fontSize: widget.isReply ? 13 : 14,
                        color: Colors.black87,
                        height: 1.3,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // 操作按钮行
                    Row(
                      children: [
                        // 回复按钮
                        if (!widget.isReply) ...[
                          GestureDetector(
                            onTap: widget.onReply,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              child: Text(
                                '回复',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                          ),
                          
                          if (widget.comment.repliesCount > 0) ...[
                            const SizedBox(width: 16),
                            Text(
                              '${widget.comment.repliesCount}条回复',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ],
                        ],
                        
                        const Spacer(),
                        
                        // 点赞按钮
                        GestureDetector(
                          onTap: _handleLike,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AnimatedBuilder(
                                  animation: _likeAnimation,
                                  builder: (context, child) {
                                    return Transform.scale(
                                      scale: _likeAnimation.value,
                                      child: Icon(
                                        _isLiked ? Icons.favorite : Icons.favorite_border,
                                        size: widget.isReply ? 14 : 16,
                                        color: _isLiked ? Colors.red : Colors.grey.shade600,
                                      ),
                                    );
                                  },
                                ),
                                if (_likesCount > 0) ...[
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatLikesCount(_likesCount),
                                    style: TextStyle(
                                      fontSize: widget.isReply ? 11 : 12,
                                      color: _isLiked ? Colors.red : Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // 回复列表
          if (widget.showReplies && widget.replies.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...widget.replies.map((reply) => TikTokStyleComment(
              comment: reply,
              isReply: true,
              onLikeChanged: widget.onLikeChanged,
            )),
            
            // 加载更多回复按钮
            if (widget.onLoadMoreReplies != null)
              GestureDetector(
                onTap: widget.onLoadMoreReplies,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 8),
                  child: Text(
                    '查看更多回复',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }
}
