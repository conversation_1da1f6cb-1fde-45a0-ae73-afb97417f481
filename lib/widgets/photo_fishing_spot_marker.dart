import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../models/spot_photo.dart';
import '../services/unified_image_service.dart';

// 🎯 重新设计的几何常量 - 基于内容区域的绝对定位
const double _kTriangleHeightFactor = 0.13; // 三角形高度 = size * 0.25

/// 带照片的钓点标记组件
///
/// 特性：
/// - 圆形照片显示在圆中心
/// - 底部有尖点指向钓点位置
/// - 支持点击交互
/// - 支持加载状态和错误处理
/// - 🔧 修复：改为StatefulWidget避免地图移动时重复刷新图片
class PhotoFishingSpotMarker extends StatefulWidget {
  final FishingSpot spot;
  final VoidCallback? onTap;
  final double size; // 整体大小
  final Offset centerOffset; // 圆心偏移量：居中基础上的偏移
  final Future<List<SpotPhoto>> Function(String spotId)? getPhotos;
  final Future<int> Function(String spotId)? getLikesCount;

  const PhotoFishingSpotMarker({
    super.key,
    required this.spot,
    this.onTap,
    required this.size,
    this.centerOffset = Offset.zero, // 默认无偏移，完全居中
    this.getPhotos,
    this.getLikesCount,
  });

  @override
  State<PhotoFishingSpotMarker> createState() => _PhotoFishingSpotMarkerState();
}

class _PhotoFishingSpotMarkerState extends State<PhotoFishingSpotMarker> 
    with AutomaticKeepAliveClientMixin {
  List<SpotPhoto> _photos = [];
  int _likesCount = 0;
  bool _isLoading = true;

  @override
  bool get wantKeepAlive => true; // 保持Widget状态，避免应用恢复时重建

  // 静态缓存，跨Widget实例共享
  static final Map<String, List<SpotPhoto>> _globalPhotoCache = {};
  static final Map<String, int> _globalLikesCache = {};
  
  /// 清理全局缓存（在用户登录状态变化时调用）
  static void clearGlobalCache() {
    _globalPhotoCache.clear();
    _globalLikesCache.clear();
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 只在初始化时加载一次数据，避免地图移动时重复加载
  Future<void> _loadData() async {
    final spotId = widget.spot.id;
    
    // 检查全局缓存
    if (_globalPhotoCache.containsKey(spotId) && _globalLikesCache.containsKey(spotId)) {
      if (mounted) {
        setState(() {
          _photos = _globalPhotoCache[spotId]!;
          _likesCount = _globalLikesCache[spotId]!;
          _isLoading = false;
        });
      }
      return;
    }
    
    
    try {
      final results = await Future.wait([
        widget.getPhotos?.call(spotId) ?? Future.value(<SpotPhoto>[]),
        widget.getLikesCount?.call(spotId) ?? Future.value(0),
      ]);

      final photos = results[0] as List<SpotPhoto>;
      final likesCount = results[1] as int;
      
      // 保存到全局缓存
      _globalPhotoCache[spotId] = photos;
      _globalLikesCache[spotId] = likesCount;
      
      if (mounted) {
        setState(() {
          _photos = photos;
          _likesCount = likesCount;
          _isLoading = false;
        });
      } else {
        debugPrint('⚠️ [PhotoMarker] Widget已销毁，跳过数据更新 - 钓点ID: $spotId');
      }
    } catch (e) {
      debugPrint('❌ [PhotoMarker] 加载数据失败 - 钓点ID: $spotId, 错误: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin
    
    
    // 加载状态时不显示任何内容
    if (_isLoading) {
      return const SizedBox.shrink();
    }

    // 正常状态：显示照片标记
    return GestureDetector(
      onTap: widget.onTap,
      onLongPress: widget.onTap,
      behavior: HitTestBehavior.opaque,
      child:CustomPaint(
          size: Size(widget.size, widget.size),
          painter: _PhotoMarkerPainter(
            likesCount: _likesCount,
            size: widget.size,
            centerOffset: widget.centerOffset,
          ),
          child:Stack(children: [_buildPhotoContent()]),
      ),
    );
  }

  /// 构建照片内容
  Widget _buildPhotoContent() {
    final photo = _photos.first;
    // 🎯 核心逻辑：照片圆形在内容区域中的绝对定位
    final actualContentSize = widget.size; // 与CustomPaint尺寸保持一致

    // 计算圆形半径：基于实际内容区域大小
    final diameter = (actualContentSize * 0.8).roundToDouble();
    final circleRadius = diameter / 2;

    // 水平位置：可调整的水平中心位置
    final circleCenterX = actualContentSize * 0.5;
    final circleLeft = circleCenterX - circleRadius;

    // 垂直位置： 控制圆心位置
    final circleCenterY = actualContentSize * 0.5;
    final circleTop = circleCenterY - circleRadius  ;

    // 应用用户自定义偏移
    final finalLeft = circleLeft + widget.centerOffset.dx;
    final finalTop = circleTop + widget.centerOffset.dy;

    
    return Positioned(
      left: finalLeft,
      top: finalTop,
      child: Container(
        // // 🔍 调试：照片容器外边界 - 绿色边框
        // decoration: BoxDecoration(
        //   border: Border.all(color: Colors.green, width: 1),
        // ),
        child: SizedBox.square(
          dimension: diameter,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),//[*参数调整*]照片外圈边框大小
            ),
            child: Container(
              // // 🔍 调试：圆形照片内部 - 黄色边框
              // decoration: BoxDecoration(
              //   border: Border.all(color: Colors.yellow, width: 1),
              // ),
              child: ClipOval(child: _buildPhotoImage(photo)),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建照片图像（优化：添加稳定key避免重复刷新）
  Widget _buildPhotoImage(SpotPhoto photo) {
    final imageService = UnifiedImageService();

    return imageService.buildCachedSignedImage(
      key: ValueKey('photo_${photo.id}'), // 🔧 关键修复：添加稳定的key
      originalUrl: photo.thumbnailUrl ?? photo.url,
      fit: BoxFit.cover, // 保持图片宽高比，配合AspectRatio确保圆形
      placeholder: const SizedBox.shrink(), // 不显示加载占位符
      errorWidget: Container(
        color: Colors.grey[200],
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey[400],
          size: 16,
        ),
      ),
    );
  }
}

/// 照片标记绘制器
/// 只负责绘制背景形状和尖点
class _PhotoMarkerPainter extends CustomPainter {
  final int likesCount;
  final double size;
  final Offset centerOffset; // 圆心偏移量

  _PhotoMarkerPainter({
    required this.likesCount,
    required this.size,
    this.centerOffset = Offset.zero,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    // debugPrint("[PhotoMarker] CustomPaint - widget.size: $size, 实际画布大小: ${canvasSize.width}x${canvasSize.height}");
    
    // // 🔍 调试：绘制CustomPaint画布背景 - 半透明紫色
    // final debugPaint =
    //     Paint()
    //       ..color = Colors.purple.withOpacity(0.3)
    //       ..style = PaintingStyle.fill;
    // canvas.drawRect(Offset.zero & canvasSize, debugPaint);

    // // 🔍 调试：绘制内容区域边界 - 橙色边框
    // final contentOffset = Offset(
    //   (canvasSize.width - size) / 2,
    //   (canvasSize.height - size) / 2,
    // );
    // final contentRect = Rect.fromLTWH(
    //   contentOffset.dx,
    //   contentOffset.dy,
    //   size,
    //   size,
    // );
    
    // debugPrint("[PhotoMarker] 橙色框大小: ${size}x${size}, 位置: (${contentOffset.dx}, ${contentOffset.dy}), 画布大小: ${canvasSize.width}x${canvasSize.height}");
    
    // final contentBorderPaint =
    //     Paint()
    //       ..color = Colors.orange
    //       ..style = PaintingStyle.stroke
    //       ..strokeWidth = 2.0;
    // canvas.drawRect(contentRect, contentBorderPaint);

    // 绘制尖点指向地面
    _drawPointer(canvas, canvasSize);
  }

  /// 绘制指向地面的尖点
  void _drawPointer(Canvas canvas, Size canvasSize) {
    // 🎯 重新设计：三角形下顶点固定在内容区域下边中点

    // 内容区域在画布中的位置（现在画布=内容区域，所以offset=0）
    final contentOffset = Offset(
      (canvasSize.width - size) / 2,
      (canvasSize.height - size) / 2,
    );

    // 🔺 三角形下顶点：内容区域下边中点
    final triangleBottomX = contentOffset.dx + size / 2; // 内容区域水平中心
    final triangleBottomY = contentOffset.dy + size*0.98; // [*参数调整*]三角形下顶点高度调整，内容区域下边

    // 🔺 三角形高度：可调参数
    final triangleHeight = size * _kTriangleHeightFactor;

    // 🔺 等边三角形：底边 = 高度 * 2 / √3 ≈ 高度 * 1.155
    final triangleBase = triangleHeight * 1.155;
    final halfBase = triangleBase / 2;

    // 🔺 三角形顶点Y坐标
    final triangleTopY = triangleBottomY - triangleHeight;

    // 🔺 构建等边三角形路径
    final Path trianglePath =
        Path()
          ..moveTo(triangleBottomX - halfBase, triangleTopY) // 左上顶点
          ..lineTo(triangleBottomX + halfBase, triangleTopY) // 右上顶点
          ..lineTo(triangleBottomX, triangleBottomY) // 下顶点
          ..close();

    // 🔺 绘制三角形填充
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..color = Colors.white;
    canvas.drawPath(trianglePath, paint);

    // 🔺 绘制三角形边框
    final borderPaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..color = Colors.white
          ..strokeWidth = 2.0;
    canvas.drawPath(trianglePath, borderPaint);

    // // 🔍 调试：标记三角形下顶点位置 - 红色圆点
    // final debugPointPaint =
    //     Paint()
    //       ..color = Colors.red
    //       ..style = PaintingStyle.fill;
    // canvas.drawCircle(
    //   Offset(triangleBottomX, triangleBottomY),
    //   3.0,
    //   debugPointPaint,
    // );
  }

  @override
  bool shouldRepaint(covariant _PhotoMarkerPainter oldDelegate) {
    // 当尺寸、偏移或点赞数发生变化时需要重绘
    return oldDelegate.size != size ||
        oldDelegate.centerOffset != centerOffset ||
        oldDelegate.likesCount != likesCount;
  }
}

/// 照片钓点标记构建器
class PhotoFishingSpotMarkerBuilder {
  /// 创建带照片的钓点标记
  ///
  /// [spot] 钓点数据
  /// [onTap] 点击回调
  /// [size] 由map_page.dart调用时传递过来的标记大小
  /// [centerOffset] 圆心偏移量：居中基础上的偏移
  /// [getPhotos] 获取照片列表的异步函数
  /// [getLikesCount] 获取点赞数量的异步函数
  static Widget buildMarker({
    required FishingSpot spot,
    VoidCallback? onTap,
    required double size,
    Offset centerOffset = Offset.zero,
    Future<List<SpotPhoto>> Function(String spotId)? getPhotos,
    Future<int> Function(String spotId)? getLikesCount,
  }) {
    // 🔧 关键修复：直接使用StatefulWidget，避免FutureBuilder重复执行
    return PhotoFishingSpotMarker(
      key: ValueKey('marker_${spot.id}'), // 添加稳定的key
      spot: spot,
      onTap: onTap,
      size: size,
      centerOffset: centerOffset,
      getPhotos: getPhotos,
      getLikesCount: getLikesCount,
    );
  }

  /// 清理全局缓存（在用户登录状态变化时调用）
  static void clearGlobalCache() {
    _PhotoFishingSpotMarkerState._globalPhotoCache.clear();
    _PhotoFishingSpotMarkerState._globalLikesCache.clear();
  }
}
