import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 水位选择器组件
///
/// 功能：
/// - 水位状态选择
/// - 可视化选择界面
/// - 状态管理
class WaterLevelSelector extends StatelessWidget {
  /// 当前选中的水位
  final String selectedWaterLevel;
  
  /// 水位变更回调
  final Function(String) onChanged;
  
  /// 是否启用
  final bool enabled;
  
  /// 水位选项
  static const List<Map<String, String>> waterLevels = [
    {'value': '正常', 'label': '正常', 'emoji': '💧'},
    {'value': '涨水', 'label': '涨水', 'emoji': '🌊'},
    {'value': '退水', 'label': '退水', 'emoji': '🏜️'},
  ];

  const WaterLevelSelector({
    super.key,
    required this.selectedWaterLevel,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '水位',
          style: AppTheme.headlineSmall,
        ),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children: waterLevels.map((level) {
              final isSelected = selectedWaterLevel == level['value'];

              return GestureDetector(
                onTap: enabled ? () => onChanged(level['value']!) : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingS,
                    vertical: AppTheme.spacingXS,
                  ),
                  decoration: AppTheme.selectorDecoration(
                    isSelected: isSelected,
                    selectedColor: AppTheme.warningColor,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        level['emoji']!,
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(width: AppTheme.spacingXS),
                      Text(
                        level['label']!,
                        style: AppTheme.labelMedium.copyWith(
                          fontSize: 13,
                          color: isSelected
                              ? AppTheme.warningColor
                              : AppTheme.textPrimaryColor,
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
