import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';
import '../../models/emoji_marker.dart';

/// 鱼种选择器组件
///
/// 功能：
/// - 鱼种类型选择
/// - 可视化选择界面
/// - 状态管理
class FishTypeSelector extends StatelessWidget {
  /// 当前选中的鱼种
  final String selectedFishType;
  
  /// 鱼种变更回调
  final Function(String) onChanged;
  
  /// 是否启用
  final bool enabled;

  const FishTypeSelector({
    super.key,
    required this.selectedFishType,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '鱼种',
          style: AppTheme.headlineSmall,
        ),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children: FishingSpotMarkers.fishTypes.map((fishType) {
              final isSelected = selectedFishType == fishType.type;

              return GestureDetector(
                onTap: enabled ? () => onChanged(fishType.type) : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingS,
                    vertical: AppTheme.spacingXS,
                  ),
                  decoration: AppTheme.selectorDecoration(
                    isSelected: isSelected,
                    selectedColor: Colors.blue,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        fishType.emoji,
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(width: AppTheme.spacingXS),
                      Text(
                        fishType.name,
                        style: AppTheme.labelMedium.copyWith(
                          fontSize: 13,
                          color: isSelected
                              ? Colors.blue.shade700
                              : AppTheme.textPrimaryColor,
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
