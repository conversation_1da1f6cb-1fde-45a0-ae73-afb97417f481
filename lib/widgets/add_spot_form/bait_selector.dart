import 'package:flutter/material.dart';
import '../../theme/unified_theme.dart';

/// 饵料选择器组件
///
/// 功能：
/// - 饵料类型选择
/// - 可视化选择界面
/// - 状态管理
class BaitSelector extends StatelessWidget {
  /// 当前选中的饵料
  final String selectedBait;

  /// 饵料变更回调
  final Function(String) onChanged;

  /// 是否启用
  final bool enabled;

  /// 饵料选项
  static const List<Map<String, String>> baits = [
    {'value': '商品饵', 'label': '商品饵', 'emoji': '🎣'},
    {'value': '自制饵', 'label': '自制饵', 'emoji': '🥖'},
    {'value': '蚯蚓', 'label': '蚯蚓', 'emoji': '🪱'},
  ];

  const BaitSelector({
    super.key,
    required this.selectedBait,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('饵料', style: AppTheme.headlineSmall),
        const SizedBox(height: AppTheme.spacingS),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            spacing: AppTheme.spacingXS,
            runSpacing: AppTheme.spacingXS,
            children:
                baits.map((bait) {
                  final isSelected = selectedBait == bait['value'];

                  return GestureDetector(
                    onTap: enabled ? () => onChanged(bait['value']!) : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingS,
                        vertical: AppTheme.spacingXS,
                      ),
                      decoration: AppTheme.selectorDecoration(
                        isSelected: isSelected,
                        selectedColor: Colors.purple,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            bait['emoji']!,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: AppTheme.spacingXS),
                          Text(
                            bait['label']!,
                            style: AppTheme.labelMedium.copyWith(
                              fontSize: 13,
                              color:
                                  isSelected
                                      ? Colors.purple.shade700
                                      : AppTheme.textPrimaryColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}
