import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'service_locator.dart';
import '../pages/spot_detail_page.dart';

/// 分享服务
///
/// 功能：
/// 1. 生成钓点分享文本
/// 2. 复制分享内容到剪贴板
/// 3. 检测剪贴板中的分享链接
/// 4. 解析分享链接并跳转到对应页面
class ShareService {
  static final ShareService _instance = ShareService._internal();
  factory ShareService() => _instance;
  ShareService._internal();

  // 分享链接的协议头
  static const String _shareProtocol = '钓鱼了么://spot/';

  // 上次检查的剪贴板内容，避免重复处理
  String? _lastClipboardContent;

  /// 生成钓点分享文本
  String generateSpotShareText({
    required String spotId,
    required String spotName,
    required String spotType,
    required String fishTypes,
    required int likesCount,
    required int commentsCount,
  }) {
    final shareText = '''🎣发现了一个绝佳钓点！

📍$spotName
🏞️类型：$spotType
🐟鱼类：$fishTypes
⭐$likesCount条点赞 💬$commentsCount条评论

🔗$_shareProtocol$spotId

复制这段文字，打开钓鱼了么App查看详情！''';

    return shareText;
  }

  /// 复制分享内容到剪贴板
  Future<bool> copyToClipboard(String content) async {
    try {
      await Clipboard.setData(ClipboardData(text: content));
      debugPrint('✅ [分享服务] 内容已复制到剪贴板');
      return true;
    } catch (e) {
      debugPrint('❌ [分享服务] 复制到剪贴板失败: $e');
      return false;
    }
  }

  /// 检测剪贴板中的分享链接
  Future<String?> detectShareLink() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final content = clipboardData?.text;

      if (content == null || content.isEmpty) {
        return null;
      }

      // 避免重复处理相同内容
      if (content == _lastClipboardContent) {
        return null;
      }

      // 检查是否包含分享链接
      if (content.contains(_shareProtocol)) {
        _lastClipboardContent = content;

        // 提取钓点ID
        final spotId = _extractSpotId(content);
        if (spotId != null) {
          debugPrint('🔍 [分享服务] 检测到分享链接，钓点ID: $spotId');
          return spotId;
        }
      }

      return null;
    } catch (e) {
      debugPrint('❌ [分享服务] 检测剪贴板失败: $e');
      return null;
    }
  }

  /// 从分享文本中提取钓点ID
  String? _extractSpotId(String content) {
    try {
      final regex = RegExp(r'钓鱼了么://spot/([a-zA-Z0-9]+)');
      final match = regex.firstMatch(content);
      return match?.group(1);
    } catch (e) {
      debugPrint('❌ [分享服务] 提取钓点ID失败: $e');
      return null;
    }
  }

  /// 处理分享链接跳转
  Future<void> handleShareLink(BuildContext context, String spotId) async {
    try {
      debugPrint('🔗 [分享服务] 开始处理分享链接跳转: $spotId');

      // 获取钓点信息（不显示加载对话框，避免BuildContext问题）
      final spot = await Services.fishingSpot.getSpotById(spotId);

      if (spot != null && context.mounted) {
        // 直接跳转到钓点详情页
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => SpotDetailPage(spot: spot)),
        );

        debugPrint('✅ [分享服务] 分享链接跳转成功');
      } else {
        // 钓点不存在或已删除
        if (context.mounted) {
          _showErrorDialog(context, '钓点不存在或已被删除');
        }
        debugPrint('❌ [分享服务] 钓点不存在: $spotId');
      }
    } catch (e) {
      debugPrint('❌ [分享服务] 处理分享链接失败: $e');

      if (context.mounted) {
        _showErrorDialog(context, '加载钓点信息失败，请稍后重试');
      }
    }
  }

  /// 显示错误对话框
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('提示'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 显示分享链接确认对话框
  Future<bool> showShareLinkDialog(BuildContext context, String spotId) async {
    debugPrint('🎯 [分享服务] 开始显示分享链接确认对话框，钓点ID: $spotId');
    debugPrint('🎯 [分享服务] Context mounted: ${context.mounted}');
    
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('🎣 发现分享链接'),
            content: const Text('检测到剪贴板中有钓点分享链接，是否要查看这个钓点？'),
            actions: [
              TextButton(
                onPressed: () {
                  debugPrint('🎯 [分享服务] 用户点击取消');
                  Navigator.of(context).pop(false);
                },
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  debugPrint('🎯 [分享服务] 用户点击查看');
                  Navigator.of(context).pop(true);
                },
                child: const Text('查看'),
              ),
            ],
          ),
    );

    debugPrint('🎯 [分享服务] 对话框关闭，结果: $result');
    
    // 无论用户选择什么，都清空剪贴板记录，避免重复处理
    clearClipboardRecord();
    debugPrint('🧹 [分享服务] 已清空剪贴板记录，避免重复处理');

    return result ?? false;
  }

  /// 清除剪贴板记录（避免重复处理）
  void clearClipboardRecord() {
    _lastClipboardContent = null;
  }
}
