import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pocketbase/pocketbase.dart';

import '../config/pocketbase_config.dart';
import '../models/fishing_activity.dart';
import '../models/user.dart';
import '../services/service_locator.dart';
import '../services/location_service.dart';

/// 钓鱼活动服务
///
/// 管理"一起钓鱼"约钓活动的创建、查询、更新等操作
/// 复用钓点服务的核心逻辑，但简化验证要求
class FishingActivityService {
  static const String _cacheKey = 'fishing_activities_cache';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // 缓存相关
  List<FishingActivity> _activitiesCache = [];
  DateTime? _lastAllActivitiesLoadTime;
  final Map<String, List<FishingActivity>> _regionCache = {};
  final Map<String, DateTime> _regionCacheTime = {};

  /// 获取当前用户
  User? get currentUser => Services.auth.currentUser;

  /// 获取PocketBase客户端
  PocketBase get pb => PocketBaseConfig.instance.client;

  /// 获取所有活动（带缓存）
  Future<List<FishingActivity>> getAllActivities({
    bool forceRefresh = false,
  }) async {
    final now = DateTime.now();

    // 检查缓存是否有效
    if (!forceRefresh &&
        _activitiesCache.isNotEmpty &&
        _lastAllActivitiesLoadTime != null &&
        now.difference(_lastAllActivitiesLoadTime!).inMinutes <
            _cacheExpiry.inMinutes) {
      debugPrint('🔍 [活动服务] 使用缓存的活动数据');
      return _filterActiveActivities(_activitiesCache);
    }

    debugPrint('🔍 [活动服务] 从服务器获取活动数据');

    try {
      // 从 PocketBase 获取活动数据
      final records = await pb
          .collection('fishing_activities')
          .getFullList(
            sort: '-created',
            expand: 'creator_id',
            filter: 'status = "active"', // 只获取活跃的活动
          );

      // 转换为FishingActivity对象并更新缓存
      _activitiesCache = _convertRecordsToActivities(records);
      _lastAllActivitiesLoadTime = now;

      // 清理区域缓存，因为全局数据已更新
      _regionCache.clear();
      _regionCacheTime.clear();

      // 异步保存到本地
      _saveActivitiesToLocal().catchError((e) => debugPrint('保存活动数据失败: $e'));

      return _filterActiveActivities(_activitiesCache);
    } catch (e) {
      debugPrint('获取活动失败: $e');

      // 如果API调用失败但有缓存，返回缓存
      if (_activitiesCache.isNotEmpty) {
        debugPrint('使用过期的活动缓存数据');
        return _filterActiveActivities(_activitiesCache);
      }

      // 如果没有缓存，尝试从本地加载
      await _loadActivitiesFromLocal();
      return _filterActiveActivities(_activitiesCache);
    }
  }

  /// 过滤掉已过期的活动
  List<FishingActivity> _filterActiveActivities(
    List<FishingActivity> activities,
  ) {
    return activities
        .where((activity) => activity.status == 'active' && !activity.isExpired)
        .toList();
  }

  /// 添加新活动
  Future<FishingActivity?> addActivity(
    FishingActivity activity, {
    Map<String, dynamic>? publishLocation,
  }) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      final userId = currentUser!.id;
      final locationService = LocationService();

      debugPrint('🔍 [活动服务] 准备创建钓鱼活动');
      debugPrint('🔍 [活动服务] 活动标题: ${activity.title}');
      debugPrint('🔍 [活动服务] 开始时间: ${activity.startTime}');
      debugPrint('🔍 [活动服务] 持续时长: ${activity.duration}小时');
      debugPrint('🔍 [活动服务] 用户ID: $userId');

      // 获取发布位置（如果没有提供，则获取当前位置）
      Map<String, dynamic>? finalPublishLocation = publishLocation;
      if (finalPublishLocation == null) {
        try {
          finalPublishLocation =
              await locationService.getCurrentLocationAsGeoPointAsync();
          debugPrint('🔍 [活动服务] 获取到发布位置: $finalPublishLocation');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 获取发布位置失败: $e');
        }
      }

      // 确保描述不为空
      final finalDescription =
          activity.description.trim().isEmpty
              ? '${activity.title} - 一起去钓鱼'
              : activity.description;

      debugPrint('🔍 [活动服务] 开始创建群聊');

      // 1. 先创建群聊
      final chatGroup = await pb
          .collection('activity_chat_groups')
          .create(
            body: {
              'group_name': '${activity.title} - 群聊',
              'creator_id': userId,
              'member_count': 1,
              'status': 'active',
              'description': '${activity.title}的活动群聊',
            },
          );

      final groupChatId = chatGroup.id;
      debugPrint('🔍 [活动服务] 群聊创建成功: $groupChatId');

      try {
        // 2. 创建活动记录，关联群聊ID
        debugPrint('🔍 [活动服务] 开始创建活动记录');
        final record = await pb
            .collection('fishing_activities')
            .create(
              body: {
                'creator_id': userId,
                'title': activity.title,
                'description': finalDescription,
                'location': activity.location,
                'start_time': activity.startTime.toIso8601String(),
                'duration': activity.duration,
                'max_participants': activity.maxParticipants,
                'current_participants': activity.currentParticipants,
                'status': activity.status,
                'images': activity.images,
                'group_chat_id': groupChatId,
              },
            );

        debugPrint('🔍 [活动服务] 活动记录创建成功: ${record.id}');

        // 3. 更新群聊记录，关联活动ID
        await pb
            .collection('activity_chat_groups')
            .update(groupChatId, body: {'activity_id': record.id});

        debugPrint('🔍 [活动服务] 群聊关联活动成功');

        // 获取完整的活动数据
        debugPrint('🔍 [活动服务] 获取完整活动数据: ${record.id}');
        final newActivity = await getActivityById(record.id);

        if (newActivity != null) {
          debugPrint('🔍 [活动服务] 获取完整数据成功');
          debugPrint('🔍 [活动服务] 最终返回的ID: ${newActivity.id}');

          // 更新缓存
          _activitiesCache.insert(0, newActivity);
          await _saveActivitiesToLocal();
        } else {
          debugPrint('❌ [活动服务] 获取完整数据失败');
        }

        debugPrint('✅ [活动服务] 添加活动成功: ${record.id}');
        return newActivity;
      } catch (e) {
        // 如果活动创建失败，删除已创建的群聊
        debugPrint('❌ [活动服务] 活动创建失败，清理群聊: $e');
        try {
          await pb.collection('activity_chat_groups').delete(groupChatId);
          debugPrint('🔍 [活动服务] 群聊清理成功');
        } catch (cleanupError) {
          debugPrint('❌ [活动服务] 群聊清理失败: $cleanupError');
        }
        rethrow;
      }
    } catch (e) {
      debugPrint('添加活动失败: $e');
      return null;
    }
  }

  /// 根据ID获取活动
  Future<FishingActivity?> getActivityById(String id) async {
    try {
      final record = await pb
          .collection('fishing_activities')
          .getOne(id, expand: 'user_id');

      return _convertRecordToActivity(record);
    } catch (e) {
      debugPrint('获取活动详情失败: $e');
      return null;
    }
  }

  /// 更新活动
  Future<bool> updateActivity(FishingActivity activity) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查活动是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_activities')
          .getOne(activity.id);

      if (existingRecord.data['creator_id'] != currentUser!.id) {
        throw Exception('无权限更新此活动');
      }

      // 更新活动数据
      await pb
          .collection('fishing_activities')
          .update(
            activity.id,
            body: {
              'title': activity.title,
              'description': activity.description,
              'location': activity.location,
              'start_time': activity.startTime.toIso8601String(),
              'duration': activity.duration,
              'max_participants': activity.maxParticipants,
              'current_participants': activity.currentParticipants,
              'status': activity.status,
              'images': activity.images,
            },
          );

      // 更新缓存
      final index = _activitiesCache.indexWhere((a) => a.id == activity.id);
      if (index != -1) {
        _activitiesCache[index] = activity;
        await _saveActivitiesToLocal();
      }

      debugPrint('更新活动成功: ${activity.id}');
      return true;
    } catch (e) {
      debugPrint('更新活动失败: $e');
      return false;
    }
  }

  /// 删除活动
  Future<bool> deleteActivity(String id) async {
    try {
      // 检查用户是否已登录
      if (currentUser == null) {
        throw Exception('用户未登录');
      }

      // 先检查活动是否属于当前用户
      final existingRecord = await pb
          .collection('fishing_activities')
          .getOne(id);

      if (existingRecord.data['creator_id'] != currentUser!.id) {
        throw Exception('无权限删除此活动');
      }

      final groupChatId = existingRecord.data['group_chat_id'];

      // 软删除：更新状态为cancelled
      await pb
          .collection('fishing_activities')
          .update(id, body: {'status': 'cancelled'});

      // 同时删除对应的群聊
      if (groupChatId != null) {
        try {
          await pb
              .collection('activity_chat_groups')
              .update(groupChatId, body: {'status': 'deleted'});
          debugPrint('🔍 [活动服务] 群聊删除成功: $groupChatId');
        } catch (e) {
          debugPrint('⚠️ [活动服务] 群聊删除失败: $e');
          // 群聊删除失败不影响活动删除
        }
      }

      // 从缓存中移除
      _activitiesCache.removeWhere((a) => a.id == id);
      await _saveActivitiesToLocal();

      debugPrint('删除活动成功: $id');
      return true;
    } catch (e) {
      debugPrint('删除活动失败: $e');
      return false;
    }
  }

  /// 转换记录为活动对象
  FishingActivity _convertRecordToActivity(dynamic record) {
    try {
      final data = record.data;
      final expand = record.expand;

      // 获取创建者信息
      String? creatorName;
      if (expand != null && expand['creator_id'] != null) {
        final userData = expand['creator_id'];
        creatorName = userData['username'] ?? userData['name'];
      }

      return FishingActivity.fromJson({...data, 'creator_name': creatorName});
    } catch (e) {
      debugPrint('❌ [活动服务] 转换活动记录失败: $e');
      debugPrint('❌ [活动服务] 记录数据: $record');
      rethrow;
    }
  }

  /// 转换记录列表为活动对象列表
  List<FishingActivity> _convertRecordsToActivities(List<dynamic> records) {
    return records.map((record) => _convertRecordToActivity(record)).toList();
  }

  /// 保存活动到本地缓存
  Future<void> _saveActivitiesToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJson = _activitiesCache.map((a) => a.toJson()).toList();
      await prefs.setString(_cacheKey, jsonEncode(activitiesJson));
      await prefs.setString(
        '${_cacheKey}_time',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      debugPrint('保存活动缓存失败: $e');
    }
  }

  /// 从本地缓存加载活动
  Future<void> _loadActivitiesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJsonString = prefs.getString(_cacheKey);
      final cacheTimeString = prefs.getString('${_cacheKey}_time');

      if (activitiesJsonString != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inMinutes < _cacheExpiry.inMinutes * 2) {
          final activitiesJson = jsonDecode(activitiesJsonString) as List;
          _activitiesCache =
              activitiesJson
                  .map((json) => FishingActivity.fromJson(json))
                  .toList();
          debugPrint('从本地缓存加载了 ${_activitiesCache.length} 个活动');
        }
      }
    } catch (e) {
      debugPrint('加载活动缓存失败: $e');
    }
  }

  /// 清理过期活动（定期调用）
  Future<void> cleanupExpiredActivities() async {
    try {
      final now = DateTime.now();

      // 获取所有活跃的活动并检查是否过期
      final activeActivities = await pb
          .collection('fishing_activities')
          .getFullList(filter: 'status = "active"');

      // 更新过期的活动状态
      for (final record in activeActivities) {
        final data = record.data;
        if (data['start_time'] != null && data['duration'] != null) {
          final startTime = DateTime.parse(data['start_time']);
          final duration = (data['duration'] as num).toDouble();
          final endTime = startTime.add(
            Duration(
              hours: duration.toInt(),
              minutes: ((duration % 1) * 60).toInt(),
            ),
          );

          if (now.isAfter(endTime)) {
            await pb
                .collection('fishing_activities')
                .update(record.id, body: {'status': 'completed'});
          }
        }
      }

      // 清理本地缓存
      _activitiesCache.removeWhere((activity) => activity.isExpired);
      await _saveActivitiesToLocal();

      debugPrint('清理过期活动完成');
    } catch (e) {
      debugPrint('清理过期活动失败: $e');
    }
  }
}
