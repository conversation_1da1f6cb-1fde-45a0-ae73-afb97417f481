import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../config/pocketbase_config.dart';
import '../models/user.dart' hide Message;
import '../models/message.dart';
import 'auth_service_new.dart';

/// 社交功能服务
///
/// 职责：
/// - 用户关注/取消关注
/// - 点赞/取消点赞
/// - 评论管理
/// - 私信功能
/// - 收藏管理
class SocialService {
  // 单例模式
  static final SocialService _instance = SocialService._internal();
  factory SocialService() => _instance;
  SocialService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();

  // 本地存储键
  static const String _messagesStorageKey = 'messages_cache';

  // 消息缓存
  List<Message> _messagesCache = [];
  
  // 点赞数缓存和请求限流
  final Map<String, int> _likesCountCache = {};
  final Map<String, DateTime> _lastRequestTime = {};

  /// 获取当前登录用户
  User? get currentUser => _authService.currentUser;

  /// 初始化社交服务
  Future<void> initialize() async {
    debugPrint('初始化社交服务');
    await _loadMessagesFromLocal();
  }

  // ==================== 关注功能 ====================

  /// 关注用户
  Future<bool> followUser(String targetUserId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      if (currentUserId == targetUserId) {
        throw Exception('不能关注自己');
      }

      // 检查是否已经关注
      try {
        await pb
            .collection('user_follows')
            .getFirstListItem(
              'follower_id = "$currentUserId" && following_id = "$targetUserId"',
            );
        // 如果找到记录，说明已经关注了
        debugPrint('用户已经关注过了');
        return true;
      } catch (_) {
        // 没有找到记录，继续创建关注关系
      }

      // 创建关注关系
      await pb
          .collection('user_follows')
          .create(
            body: {'follower_id': currentUserId, 'following_id': targetUserId},
          );

      debugPrint('关注用户成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('关注用户失败: $e');
      return false;
    }
  }

  /// 取消关注用户
  Future<bool> unfollowUser(String targetUserId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 查找并删除关注关系
      final followRecord = await pb
          .collection('user_follows')
          .getFirstListItem(
            'follower_id = "$currentUserId" && following_id = "$targetUserId"',
          );

      await pb.collection('user_follows').delete(followRecord.id);

      debugPrint('取消关注用户成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('取消关注用户失败: $e');
      return false;
    }
  }

  /// 检查是否关注了某个用户
  Future<bool> isFollowing(String targetUserId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) return false;

      await pb
          .collection('user_follows')
          .getFirstListItem(
            'follower_id = "$currentUserId" && following_id = "$targetUserId"',
          );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取用户的关注列表
  Future<List<User>> getFollowing(String userId) async {
    try {
      final records = await pb
          .collection('user_follows')
          .getFullList(
            filter: 'follower_id = "$userId"',
            expand: 'following_id',
          );

      final users = <User>[];
      for (final record in records) {
        final followingData = record.get<dynamic>('expand.following_id');
        if (followingData != null) {
          users.add(User.fromJson(followingData.toJson()));
        }
      }

      return users;
    } catch (e) {
      debugPrint('获取关注列表失败: $e');
      return [];
    }
  }

  /// 获取用户的粉丝列表
  Future<List<User>> getFollowers(String userId) async {
    try {
      final records = await pb
          .collection('user_follows')
          .getFullList(
            filter: 'following_id = "$userId"',
            expand: 'follower_id',
          );

      final users = <User>[];
      for (final record in records) {
        final followerData = record.get<dynamic>('expand.follower_id');
        if (followerData != null) {
          users.add(User.fromJson(followerData.toJson()));
        }
      }

      return users;
    } catch (e) {
      debugPrint('获取粉丝列表失败: $e');
      return [];
    }
  }

  // ==================== 点赞功能 ====================

  /// 点赞钓点
  Future<bool> likeSpot(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 检查是否已经点赞
      try {
        await pb
            .collection('spot_likes')
            .getFirstListItem(
              'user_id = "$currentUserId" && spot_id = "$spotId"',
            );
        // 如果找到记录，说明已经点赞了
        debugPrint('用户已经点赞过了');
        return true;
      } catch (_) {
        // 没有找到记录，继续创建点赞关系
      }

      // 创建点赞记录
      await pb
          .collection('spot_likes')
          .create(
            body: {
              'user_id': currentUserId,
              'spot_id': spotId,
              'is_like': true,
            },
          );

      // 同时更新钓点的点赞计数（原子操作）
      try {
        await pb.collection('fishing_spots').update(spotId, body: {
          'likes_count+': 1, // PocketBase 原子递增操作
          'likes_count_updated_at': DateTime.now().toIso8601String(),
        });
        // 成功更新点赞数
      } catch (e) {
        debugPrint('更新钓点点赞数失败: $e');
        // 点赞记录已创建，即使计数更新失败也不影响核心功能
      }

      debugPrint('点赞钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('点赞钓点失败: $e');
      return false;
    }
  }

  /// 取消点赞钓点
  Future<bool> unlikeSpot(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 查找并删除点赞记录
      final likeRecord = await pb
          .collection('spot_likes')
          .getFirstListItem(
            'user_id = "$currentUserId" && spot_id = "$spotId"',
          );

      await pb.collection('spot_likes').delete(likeRecord.id);

      // 同时更新钓点的点赞计数（原子操作）
      try {
        await pb.collection('fishing_spots').update(spotId, body: {
          'likes_count-': 1, // PocketBase 原子递减操作
          'likes_count_updated_at': DateTime.now().toIso8601String(),
        });
        // 成功更新点赞数
      } catch (e) {
        debugPrint('更新钓点点赞数失败: $e');
        // 点赞记录已删除，即使计数更新失败也不影响核心功能
      }

      debugPrint('取消点赞钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('取消点赞钓点失败: $e');
      return false;
    }
  }

  /// 检查是否点赞了某个钓点
  Future<bool> isSpotLiked(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) return false;

      await pb
          .collection('spot_likes')
          .getFirstListItem(
            'user_id = "$currentUserId" && spot_id = "$spotId"',
          );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取钓点的点赞数（现在直接从钓点对象获取，无需额外查询）
  /// 这个方法保留用于向后兼容，但实际上点赞数已经存储在 FishingSpot.likesCount 中
  Future<int> getSpotLikesCount(String spotId) async {
    // 直接返回缓存值，避免重复的调试信息
    return _likesCountCache[spotId] ?? 0;
  }

  // ==================== 评论功能 ====================

  /// 添加评论
  Future<bool> addComment(String spotId, String content, {int? rating}) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      await pb
          .collection('comments')
          .create(
            body: {
              'spot_id': spotId,
              'user_id': currentUserId,
              'content': content,
              if (rating != null) 'rating': rating,
            },
          );

      debugPrint('添加评论成功');
      return true;
    } catch (e) {
      debugPrint('添加评论失败: $e');
      return false;
    }
  }

  /// 删除评论
  Future<bool> deleteComment(String commentId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 检查评论是否属于当前用户
      final comment = await pb.collection('comments').getOne(commentId);
      if (comment.data['user_id'] != currentUserId) {
        throw Exception('无权限删除此评论');
      }

      await pb.collection('comments').delete(commentId);

      debugPrint('删除评论成功');
      return true;
    } catch (e) {
      debugPrint('删除评论失败: $e');
      return false;
    }
  }

  // ==================== 收藏功能 ====================

  /// 收藏钓点
  Future<bool> favoriteSpot(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 检查是否已经收藏
      try {
        await pb
            .collection('user_favorites')
            .getFirstListItem(
              'user_id = "$currentUserId" && spot_id = "$spotId"',
            );
        // 如果找到记录，说明已经收藏了
        debugPrint('用户已经收藏过了');
        return true;
      } catch (_) {
        // 没有找到记录，继续创建收藏关系
      }

      // 创建收藏关系
      await pb
          .collection('user_favorites')
          .create(body: {'user_id': currentUserId, 'spot_id': spotId});

      debugPrint('收藏钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('收藏钓点失败: $e');
      return false;
    }
  }

  /// 取消收藏钓点
  Future<bool> unfavoriteSpot(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      // 查找并删除收藏关系
      final favoriteRecord = await pb
          .collection('user_favorites')
          .getFirstListItem(
            'user_id = "$currentUserId" && spot_id = "$spotId"',
          );

      await pb.collection('user_favorites').delete(favoriteRecord.id);

      debugPrint('取消收藏钓点成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('取消收藏钓点失败: $e');
      return false;
    }
  }

  /// 检查是否收藏了某个钓点
  Future<bool> isSpotFavorited(String spotId) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) return false;

      await pb
          .collection('user_favorites')
          .getFirstListItem(
            'user_id = "$currentUserId" && spot_id = "$spotId"',
          );
      return true;
    } catch (e) {
      return false;
    }
  }

  // ==================== 私信功能 ====================

  /// 发送消息
  Future<Message?> sendMessage(String receiverId, String content) async {
    try {
      final currentUserId = currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }

      final message = Message(
        senderId: currentUserId,
        receiverId: receiverId,
        content: content,
      );

      _messagesCache.add(message);
      await _saveMessagesToLocal();

      debugPrint('发送消息成功');
      return message;
    } catch (e) {
      debugPrint('发送消息失败: $e');
      return null;
    }
  }

  /// 获取与某用户的对话
  Future<List<Message>> getConversation(String otherUserId) async {
    final currentUserId = currentUser?.id;
    if (currentUserId == null) return [];

    await _loadMessagesFromLocal();

    return _messagesCache
        .where(
          (message) =>
              (message.senderId == currentUserId &&
                  message.receiverId == otherUserId) ||
              (message.senderId == otherUserId &&
                  message.receiverId == currentUserId),
        )
        .toList()
      ..sort((a, b) => a.sentAt.compareTo(b.sentAt));
  }

  /// 获取所有消息
  Future<List<Message>> getAllMessages() async {
    await _loadMessagesFromLocal();
    return _messagesCache;
  }

  // ==================== 私有方法 ====================

  /// 从本地存储加载消息数据
  Future<void> _loadMessagesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesStorageKey);

      if (messagesJson != null) {
        final List<dynamic> decodedList = jsonDecode(messagesJson);
        _messagesCache =
            decodedList.map((item) => Message.fromJson(item)).toList();
      }
    } catch (e) {
      debugPrint('从本地加载消息数据失败: $e');
      _messagesCache = [];
    }
  }

  /// 保存消息数据到本地存储
  Future<void> _saveMessagesToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = jsonEncode(
        _messagesCache.map((message) => message.toJson()).toList(),
      );
      await prefs.setString(_messagesStorageKey, messagesJson);
    } catch (e) {
      debugPrint('保存消息数据到本地失败: $e');
    }
  }

  /// 清除所有缓存（包括本地存储）
  ///
  /// 在用户切换时调用，确保不同用户之间的数据隔离
  Future<void> clearAllCache() async {
    try {
      // 清除内存缓存
      _messagesCache.clear();
      _likesCountCache.clear();
      _lastRequestTime.clear();
      debugPrint('🧹 [社交服务] 已清除内存缓存');

      // 清除本地存储缓存
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_messagesStorageKey);
      debugPrint('🧹 [社交服务] 已清除本地存储缓存');

      debugPrint('✅ [社交服务] 所有缓存已清理完成');
    } catch (e) {
      debugPrint('❌ [社交服务] 清理缓存时出错: $e');
    }
  }
}
