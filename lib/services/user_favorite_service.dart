import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import 'service_locator.dart';

/// 用户收藏服务
/// 
/// 功能：
/// 1. 添加/取消收藏钓点
/// 2. 获取用户收藏状态
/// 3. 获取用户收藏列表
class UserFavoriteService {
  static final UserFavoriteService _instance = UserFavoriteService._internal();
  factory UserFavoriteService() => _instance;
  UserFavoriteService._internal();

  // 缓存用户收藏状态
  final Map<String, bool> _favoriteCache = {};

  /// 初始化服务
  Future<void> initialize() async {
    debugPrint('🔖 [收藏服务] 初始化收藏服务');
    await _loadUserFavorites();
  }

  /// 加载用户收藏列表
  Future<void> _loadUserFavorites() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('🔖 [收藏服务] 用户未登录，跳过加载收藏');
        return;
      }

      debugPrint('🔖 [收藏服务] 开始加载用户收藏列表');
      
      final pb = PocketBaseConfig.instance.client;
      final records = await pb
          .collection('user_favorites')
          .getFullList(
            filter: 'user_id = "${currentUser.id}"',
          );

      // 清除旧缓存
      _favoriteCache.clear();

      // 更新缓存
      for (final record in records) {
        final spotId = record.data['spot_id'] as String;
        _favoriteCache[spotId] = true;
      }

      debugPrint('✅ [收藏服务] 收藏列表加载完成，共 ${_favoriteCache.length} 个收藏');
    } catch (e) {
      debugPrint('❌ [收藏服务] 加载收藏列表失败: $e');
    }
  }

  /// 检查钓点是否已收藏
  bool isFavorited(String spotId) {
    return _favoriteCache[spotId] ?? false;
  }

  /// 添加收藏
  Future<bool> addFavorite(String spotId) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [收藏服务] 用户未登录，无法添加收藏');
        return false;
      }

      debugPrint('🔖 [收藏服务] 开始添加收藏: $spotId');

      final pb = PocketBaseConfig.instance.client;
      
      // 检查是否已经收藏
      if (isFavorited(spotId)) {
        debugPrint('⚠️ [收藏服务] 钓点已收藏: $spotId');
        return true;
      }

      // 添加收藏记录
      await pb.collection('user_favorites').create(body: {
        'user_id': currentUser.id,
        'spot_id': spotId,
      });

      // 更新缓存
      _favoriteCache[spotId] = true;

      debugPrint('✅ [收藏服务] 添加收藏成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('❌ [收藏服务] 添加收藏失败: $spotId - $e');
      return false;
    }
  }

  /// 取消收藏
  Future<bool> removeFavorite(String spotId) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [收藏服务] 用户未登录，无法取消收藏');
        return false;
      }

      debugPrint('🔖 [收藏服务] 开始取消收藏: $spotId');

      final pb = PocketBaseConfig.instance.client;
      
      // 查找收藏记录
      final records = await pb
          .collection('user_favorites')
          .getFullList(
            filter: 'user_id = "${currentUser.id}" && spot_id = "$spotId"',
          );

      if (records.isEmpty) {
        debugPrint('⚠️ [收藏服务] 未找到收藏记录: $spotId');
        // 更新缓存状态
        _favoriteCache[spotId] = false;
        return true;
      }

      // 删除收藏记录
      for (final record in records) {
        await pb.collection('user_favorites').delete(record.id);
      }

      // 更新缓存
      _favoriteCache[spotId] = false;

      debugPrint('✅ [收藏服务] 取消收藏成功: $spotId');
      return true;
    } catch (e) {
      debugPrint('❌ [收藏服务] 取消收藏失败: $spotId - $e');
      return false;
    }
  }

  /// 切换收藏状态
  Future<bool> toggleFavorite(String spotId) async {
    if (isFavorited(spotId)) {
      return await removeFavorite(spotId);
    } else {
      return await addFavorite(spotId);
    }
  }

  /// 获取用户收藏的钓点列表
  Future<List<String>> getUserFavoriteSpotIds() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [收藏服务] 用户未登录，无法获取收藏列表');
        return [];
      }

      await _loadUserFavorites(); // 刷新缓存
      
      return _favoriteCache.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();
    } catch (e) {
      debugPrint('❌ [收藏服务] 获取收藏列表失败: $e');
      return [];
    }
  }

  /// 获取收藏统计
  Map<String, dynamic> getFavoriteStats() {
    final favoriteCount = _favoriteCache.values.where((v) => v).length;
    
    return {
      'totalFavorites': favoriteCount,
      'cacheSize': _favoriteCache.length,
    };
  }

  /// 清除缓存
  void clearCache() {
    _favoriteCache.clear();
    debugPrint('🧹 [收藏服务] 缓存已清除');
  }

  /// 用户登出时清理
  void dispose() {
    clearCache();
    debugPrint('🔖 [收藏服务] 服务已清理');
  }
}