import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import '../models/spot_comment.dart';
import 'service_locator.dart';

/// 钓点评论服务
///
/// 功能：
/// 1. 发布评论和回复
/// 2. 获取评论列表（按点赞数排序）
/// 3. 点赞/取消点赞评论
/// 4. 获取回复列表
/// 5. 删除评论（仅自己的评论）
class SpotCommentService {
  static final SpotCommentService _instance = SpotCommentService._internal();
  factory SpotCommentService() => _instance;
  SpotCommentService._internal();

  /// 发布评论或回复
  Future<bool> addComment({
    required String spotId,
    required String content,
    String? parentCommentId,
    String? replyToUserId,
    String? replyToUsername,
  }) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [评论服务] 用户未登录，无法发布评论');
        return false;
      }

      debugPrint('💬 [评论服务] 开始发布评论: $spotId');

      final pb = PocketBaseConfig.instance.client;
      
      final body = {
        'spot_id': spotId,
        'user_id': currentUser.id,
        'username': currentUser.username,
        'content': content,
        'likes_count': 0,
        'created_at': DateTime.now().toIso8601String(),
      };

      // 如果是回复评论，添加相关字段
      if (parentCommentId != null) {
        body['parent_comment_id'] = parentCommentId;
      }
      if (replyToUserId != null) {
        body['reply_to_user_id'] = replyToUserId;
      }
      if (replyToUsername != null) {
        body['reply_to_username'] = replyToUsername;
      }

      await pb.collection('spot_comments').create(body: body);

      debugPrint('✅ [评论服务] 评论发布成功');
      return true;
    } catch (e) {
      debugPrint('❌ [评论服务] 发布评论失败: $e');
      return false;
    }
  }

  /// 获取钓点评论列表（主评论，按点赞数排序）
  Future<List<SpotComment>> getSpotComments(String spotId, {int page = 1, int perPage = 20}) async {
    try {
      debugPrint('💬 [评论服务] 开始获取评论列表: $spotId');

      final pb = PocketBaseConfig.instance.client;
      final currentUser = Services.auth.currentUser;

      final records = await pb
          .collection('spot_comments')
          .getList(
            page: page,
            perPage: perPage,
            filter: 'spot_id = "$spotId" && parent_comment_id = ""',
            sort: '-likes_count,-created_at', // 按点赞数倒序，然后按时间倒序
          );

      final comments = <SpotComment>[];
      for (final record in records.items) {
        final commentData = record.toJson();

        // 检查当前用户是否已点赞
        if (currentUser != null) {
          commentData['is_liked_by_current_user'] = await _checkUserLikedComment(record.id, currentUser.id);
        }

        // 获取回复数量
        commentData['replies_count'] = await _getCommentRepliesCount(record.id);

        comments.add(SpotComment.fromJson(commentData));
      }

      debugPrint('✅ [评论服务] 获取到 ${comments.length} 条主评论');
      return comments;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取评论列表失败: $e');
      return [];
    }
  }

  /// 删除评论（仅限自己的评论）
  Future<bool> deleteComment(String commentId) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [评论服务] 用户未登录，无法删除评论');
        return false;
      }

      debugPrint('💬 [评论服务] 开始删除评论: $commentId');

      final pb = PocketBaseConfig.instance.client;
      
      // 先检查评论是否属于当前用户
      final comment = await pb.collection('spot_comments').getOne(commentId);
      if (comment.data['user_id'] != currentUser.id) {
        debugPrint('❌ [评论服务] 无权删除他人评论');
        return false;
      }

      await pb.collection('spot_comments').delete(commentId);

      debugPrint('✅ [评论服务] 评论删除成功');
      return true;
    } catch (e) {
      debugPrint('❌ [评论服务] 删除评论失败: $e');
      return false;
    }
  }

  /// 获取评论统计
  Future<int> getCommentCount(String spotId) async {
    try {
      final pb = PocketBaseConfig.instance.client;

      final result = await pb
          .collection('spot_comments')
          .getList(
            page: 1,
            perPage: 1,
            filter: 'spot_id = "$spotId"',
          );

      return result.totalItems;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取评论数量失败: $e');
      return 0;
    }
  }

  /// 获取评论的回复列表
  Future<List<SpotComment>> getCommentReplies(String commentId, {int page = 1, int perPage = 10}) async {
    try {
      debugPrint('💬 [评论服务] 开始获取回复列表: $commentId');

      final pb = PocketBaseConfig.instance.client;
      final currentUser = Services.auth.currentUser;

      final records = await pb
          .collection('spot_comments')
          .getList(
            page: page,
            perPage: perPage,
            filter: 'parent_comment_id = "$commentId"',
            sort: '-likes_count,-created_at', // 按点赞数倒序，然后按时间倒序
          );

      final replies = <SpotComment>[];
      for (final record in records.items) {
        final commentData = record.toJson();

        // 检查当前用户是否已点赞
        if (currentUser != null) {
          commentData['is_liked_by_current_user'] = await _checkUserLikedComment(record.id, currentUser.id);
        }

        replies.add(SpotComment.fromJson(commentData));
      }

      debugPrint('✅ [评论服务] 获取到 ${replies.length} 条回复');
      return replies;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取回复列表失败: $e');
      return [];
    }
  }

  /// 点赞/取消点赞评论
  Future<bool> toggleCommentLike(String commentId) async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [评论服务] 用户未登录，无法点赞');
        return false;
      }

      debugPrint('💬 [评论服务] 开始切换评论点赞状态: $commentId');

      // 检查是否已经点赞
      final isLiked = await _checkUserLikedComment(commentId, currentUser.id);

      if (isLiked) {
        // 取消点赞
        await _removeLike(commentId, currentUser.id);
        await _decrementLikesCount(commentId);
      } else {
        // 添加点赞
        await _addLike(commentId, currentUser.id);
        await _incrementLikesCount(commentId);
      }

      debugPrint('✅ [评论服务] 点赞状态切换成功');
      return true;
    } catch (e) {
      debugPrint('❌ [评论服务] 切换点赞状态失败: $e');
      return false;
    }
  }

  // ==================== 私有辅助方法 ====================

  /// 检查用户是否已点赞评论
  Future<bool> _checkUserLikedComment(String commentId, String userId) async {
    try {
      final pb = PocketBaseConfig.instance.client;

      final result = await pb
          .collection('comment_likes')
          .getList(
            page: 1,
            perPage: 1,
            filter: 'comment_id = "$commentId" && user_id = "$userId"',
          );

      return result.totalItems > 0;
    } catch (e) {
      debugPrint('❌ [评论服务] 检查点赞状态失败: $e');
      return false;
    }
  }

  /// 获取评论的回复数量
  Future<int> _getCommentRepliesCount(String commentId) async {
    try {
      final pb = PocketBaseConfig.instance.client;

      final result = await pb
          .collection('spot_comments')
          .getList(
            page: 1,
            perPage: 1,
            filter: 'parent_comment_id = "$commentId"',
          );

      return result.totalItems;
    } catch (e) {
      debugPrint('❌ [评论服务] 获取回复数量失败: $e');
      return 0;
    }
  }

  /// 添加点赞记录
  Future<void> _addLike(String commentId, String userId) async {
    final pb = PocketBaseConfig.instance.client;

    await pb.collection('comment_likes').create(body: {
      'comment_id': commentId,
      'user_id': userId,
    });
  }

  /// 移除点赞记录
  Future<void> _removeLike(String commentId, String userId) async {
    final pb = PocketBaseConfig.instance.client;

    final records = await pb
        .collection('comment_likes')
        .getFullList(
          filter: 'comment_id = "$commentId" && user_id = "$userId"',
        );

    for (final record in records) {
      await pb.collection('comment_likes').delete(record.id);
    }
  }

  /// 增加评论点赞数
  Future<void> _incrementLikesCount(String commentId) async {
    final pb = PocketBaseConfig.instance.client;

    final comment = await pb.collection('spot_comments').getOne(commentId);
    final currentCount = comment.data['likes_count'] ?? 0;

    await pb.collection('spot_comments').update(commentId, body: {
      'likes_count': currentCount + 1,
    });
  }

  /// 减少评论点赞数
  Future<void> _decrementLikesCount(String commentId) async {
    final pb = PocketBaseConfig.instance.client;

    final comment = await pb.collection('spot_comments').getOne(commentId);
    final currentCount = comment.data['likes_count'] ?? 0;

    await pb.collection('spot_comments').update(commentId, body: {
      'likes_count': (currentCount - 1).clamp(0, double.infinity).toInt(),
    });
  }
}