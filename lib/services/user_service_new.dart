import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import '../config/pocketbase_config.dart';
import '../models/user.dart';
import 'auth_service_new.dart';
import 'unified_image_service.dart';

/// 用户管理服务
///
/// 职责：
/// - 用户信息管理和CRUD操作
/// - 用户查询和搜索
/// - 用户数据缓存
/// - 用户统计信息
class UserService {
  // 单例模式
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();

  // 本地存储键
  static const String _usersStorageKey = 'users_cache';

  // 用户数据缓存
  List<User> _usersCache = [];
  DateTime _lastCacheTime = DateTime.now().subtract(const Duration(hours: 1));
  static const int _cacheDurationMinutes = 5;

  /// 获取当前登录用户
  User? get currentUser => _authService.currentUser;

  /// 监听当前用户变化
  ValueListenable<User?> get currentUserNotifier =>
      _authService.currentUserNotifier;

  /// 检查用户是否已登录
  bool get isLoggedIn => _authService.isLoggedIn;

  /// 初始化用户服务
  Future<void> initialize() async {
    debugPrint('初始化用户服务');

    // 监听认证状态变化
    _authService.currentUserNotifier.addListener(_onAuthStateChanged);

    // 只有在用户已登录时才加载本地缓存
    if (_authService.isLoggedIn) {
      await _loadUsersFromLocal();
      debugPrint('用户已登录，加载了本地用户缓存');
    } else {
      debugPrint('用户未登录，跳过加载本地用户缓存');
    }
  }

  /// 认证状态变化处理
  void _onAuthStateChanged() {
    final currentUser = _authService.currentUser;
    if (currentUser != null && _usersCache.isEmpty) {
      // 用户刚登录且缓存为空，加载缓存
      _loadUsersFromLocal()
          .then((_) {
            debugPrint('用户登录后自动加载了用户缓存');
          })
          .catchError((e) {
            debugPrint('用户登录后加载缓存失败: $e');
          });
    } else if (currentUser == null) {
      // 用户登出，清理缓存
      clearCache();
      debugPrint('用户登出，已清理用户缓存');
    }
  }

  /// 根据ID获取用户
  Future<User?> getUserById(String id) async {
    try {
      // 先从缓存查找
      final cachedUser = _usersCache.firstWhere(
        (user) => user.id == id,
        orElse: () => throw StateError('User not found in cache'),
      );

      // 如果缓存中有且不过期，直接返回
      if (_isCacheValid()) {
        return cachedUser;
      }
    } catch (e) {
      // 缓存中没有，继续从网络获取
    }

    try {
      final record = await pb.collection('users').getOne(id);
      final user = User.fromJson(record.toJson());

      // 更新缓存
      _updateUserInCache(user);

      return user;
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 根据用户名获取用户
  Future<User?> getUserByUsername(String username) async {
    try {
      // 先从缓存查找
      if (_isCacheValid()) {
        try {
          final cachedUser = _usersCache.firstWhere(
            (user) => user.username == username,
          );
          return cachedUser;
        } catch (e) {
          // 缓存中没有，继续从网络获取
        }
      }

      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: 1, filter: 'username = "$username"');

      if (records.items.isNotEmpty) {
        final user = User.fromJson(records.items.first.toJson());
        _updateUserInCache(user);
        return user;
      }
      return null;
    } catch (e) {
      debugPrint('获取用户失败: $e');
      return null;
    }
  }

  /// 搜索用户
  Future<List<User>> searchUsers(String query, {int limit = 20}) async {
    try {
      final records = await pb
          .collection('users')
          .getList(page: 1, perPage: limit, filter: 'username ~ "$query"');

      final users =
          records.items
              .map((record) => User.fromJson(record.toJson()))
              .toList();

      // 更新缓存
      for (final user in users) {
        _updateUserInCache(user);
      }

      return users;
    } catch (e) {
      debugPrint('搜索用户失败: $e');
      return [];
    }
  }

  /// 获取所有用户（带缓存）
  Future<List<User>> getAllUsers() async {
    // 检查缓存是否有效
    if (_usersCache.isNotEmpty && _isCacheValid()) {
      debugPrint('使用缓存的用户数据');
      return _usersCache;
    }

    try {
      final records = await pb.collection('users').getFullList();

      if (records.isEmpty) {
        debugPrint('API返回空用户列表');
        return _usersCache; // 返回缓存数据
      }

      // 更新缓存
      _usersCache =
          records.map((record) => User.fromJson(record.toJson())).toList();
      _lastCacheTime = DateTime.now();

      // 异步保存到本地
      _saveUsersToLocal().catchError((e) => debugPrint('保存用户数据失败: $e'));

      return _usersCache;
    } catch (e) {
      debugPrint('获取所有用户失败: $e');
      return _usersCache; // 返回缓存数据
    }
  }

  /// 更新用户信息
  Future<bool> updateUser(User user) async {
    try {
      // 检查权限：只能更新自己的信息
      if (currentUser?.id != user.id) {
        throw Exception('无权限更新其他用户信息');
      }

      // 更新 PocketBase 数据库
      await pb
          .collection('users')
          .update(
            user.id,
            body: {
              'username': user.username,
              'bio': user.bio,
              'phone': user.phone,
              'avatar': user.avatar,
            },
          );

      // 更新缓存
      _updateUserInCache(user);

      debugPrint('用户信息更新成功: ${user.username}');
      return true;
    } catch (e) {
      debugPrint('更新用户信息失败: $e');
      return false;
    }
  }

  /// 更新用户头像（上传到R2并存储URL到PocketBase）
  Future<bool> updateUserAvatar(String userId, String avatarPath) async {
    try {
      // 检查权限
      if (currentUser?.id != userId) {
        throw Exception('无权限更新其他用户头像');
      }

      debugPrint('🔄 [头像上传] 开始上传头像到R2');
      debugPrint('🔄 [头像上传] 用户ID: $userId');
      debugPrint('🔄 [头像上传] 文件路径: $avatarPath');

      final file = File(avatarPath);

      // 验证文件存在
      if (!await file.exists()) {
        throw Exception('头像文件不存在');
      }

      // 验证文件大小 (5MB)
      final fileSize = await file.length();
      const maxSize = 5 * 1024 * 1024;
      if (fileSize > maxSize) {
        throw Exception('文件大小不能超过5MB');
      }

      // 1. 先上传到R2获取URL
      final imageService = UnifiedImageService();
      final uploadResult = await imageService.uploadAvatar(
        imageFile: file,
        userId: userId,
      );

      if (uploadResult == null) {
        throw Exception('头像上传到R2失败');
      }

      debugPrint('✅ [头像上传] R2上传成功: ${uploadResult.originalUrl}');

      // 2. 将R2 URL更新到PocketBase用户记录
      await pb
          .collection('users')
          .update(userId, body: {'avatar': uploadResult.originalUrl});

      debugPrint('✅ [头像上传] PocketBase更新成功');

      // 获取更新后的用户信息
      final updatedRecord = await pb.collection('users').getOne(userId);
      final updatedUser = User.fromJson(updatedRecord.toJson());

      debugPrint('✅ [头像上传] 新头像URL: ${updatedUser.avatar}');

      // 更新缓存
      _updateUserInCache(updatedUser);

      // 如果是当前用户，更新认证服务中的用户信息
      if (currentUser?.id == userId) {
        _authService.updateCurrentUser(updatedUser);
      }

      debugPrint('✅ [头像上传] 用户头像更新成功');
      return true;
    } catch (e) {
      debugPrint('❌ [头像上传] 更新用户头像失败: $e');
      return false;
    }
  }

  /// 删除用户头像
  Future<bool> deleteUserAvatar(String userId) async {
    try {
      // 检查权限
      if (currentUser?.id != userId) {
        throw Exception('无权限删除其他用户头像');
      }

      debugPrint('🗑️ [头像删除] 开始删除用户头像');
      debugPrint('🗑️ [头像删除] 用户ID: $userId');

      // 将头像字段设置为空字符串或null
      await pb.collection('users').update(userId, body: {'avatar': ''});

      debugPrint('✅ [头像删除] PocketBase更新成功');

      // 获取更新后的用户信息
      final updatedRecord = await pb.collection('users').getOne(userId);
      final updatedUser = User.fromJson(updatedRecord.toJson());

      debugPrint('✅ [头像删除] 头像已清空: ${updatedUser.avatar}');

      // 更新缓存
      _updateUserInCache(updatedUser);

      // 如果是当前用户，更新认证服务中的用户信息
      if (currentUser?.id == userId) {
        _authService.updateCurrentUser(updatedUser);
      }

      debugPrint('✅ [头像删除] 用户头像删除成功');
      return true;
    } catch (e) {
      debugPrint('❌ [头像删除] 删除用户头像失败: $e');
      return false;
    }
  }

  /// 获取用户统计信息
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      // 获取用户发布的钓点数量
      final spotsCount = await pb
          .collection('fishing_spots')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的关注数
      final followingCount = await pb
          .collection('user_follows')
          .getList(page: 1, perPage: 1, filter: 'follower_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的粉丝数
      final followersCount = await pb
          .collection('user_follows')
          .getList(page: 1, perPage: 1, filter: 'following_id = "$userId"')
          .then((result) => result.totalItems);

      // 获取用户的收藏数
      final favoritesCount = await pb
          .collection('user_favorites')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"')
          .then((result) => result.totalItems);

      return {
        'spots': spotsCount,
        'following': followingCount,
        'followers': followersCount,
        'favorites': favoritesCount,
      };
    } catch (e) {
      debugPrint('获取用户统计信息失败: $e');
      return {'spots': 0, 'following': 0, 'followers': 0, 'favorites': 0};
    }
  }

  /// 更新用户积分
  ///
  /// [userId] 用户ID
  /// [pointsChange] 积分变化量（正数为增加，负数为减少）
  /// [reason] 积分变化原因
  ///
  /// 返回是否更新成功
  Future<bool> updateUserPoints(
    String userId,
    int pointsChange, {
    String? reason,
  }) async {
    try {
      // 获取当前用户信息
      final user = await getUserById(userId);
      if (user == null) {
        throw Exception('用户不存在');
      }

      final newPoints = user.points + pointsChange;
      if (newPoints < 0) {
        throw Exception('积分不足，当前积分: ${user.points}，需要: ${-pointsChange}');
      }

      // 更新数据库中的积分
      await pb.collection('users').update(userId, body: {'points': newPoints});

      // 更新缓存中的用户信息
      final updatedUser = user.copyWith(points: newPoints);
      _updateUserInCache(updatedUser);

      // 如果是当前用户，更新认证服务中的用户信息
      if (currentUser?.id == userId) {
        _authService.updateCurrentUser(updatedUser);
      }

      debugPrint(
        '✅ [积分更新] 用户 $userId 积分更新成功: ${user.points} -> $newPoints (${pointsChange > 0 ? '+' : ''}$pointsChange)${reason != null ? ', 原因: $reason' : ''}',
      );
      return true;
    } catch (e) {
      debugPrint('❌ [积分更新] 更新用户积分失败: $e');
      return false;
    }
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    final now = DateTime.now();
    return now.difference(_lastCacheTime).inMinutes < _cacheDurationMinutes;
  }

  /// 更新缓存中的用户
  void _updateUserInCache(User user) {
    final index = _usersCache.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _usersCache[index] = user;
    } else {
      _usersCache.add(user);
    }
  }

  /// 从本地存储加载用户数据
  Future<void> _loadUsersFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersStorageKey);

      if (usersJson != null) {
        final List<dynamic> decodedList = jsonDecode(usersJson);
        _usersCache = decodedList.map((item) => User.fromJson(item)).toList();
        debugPrint('从本地加载了 ${_usersCache.length} 个用户');
      }
    } catch (e) {
      debugPrint('从本地加载用户数据失败: $e');
      _usersCache = [];
    }
  }

  /// 保存用户数据到本地存储
  Future<void> _saveUsersToLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(
        _usersCache.map((user) => user.toJson()).toList(),
      );
      await prefs.setString(_usersStorageKey, usersJson);
      debugPrint('保存了 ${_usersCache.length} 个用户到本地');
    } catch (e) {
      debugPrint('保存用户数据到本地失败: $e');
    }
  }

  /// 清除缓存
  void clearCache() {
    _usersCache.clear();
    _lastCacheTime = DateTime.now().subtract(const Duration(hours: 1));
  }

  /// 清除所有缓存（包括本地存储）
  Future<void> clearAllCache() async {
    try {
      // 清除内存缓存
      clearCache();

      // 清除本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_usersStorageKey);

      debugPrint('用户缓存已完全清除');
    } catch (e) {
      debugPrint('清除用户缓存失败: $e');
    }
  }

  /// 静态方法：清除本地存储的用户缓存（用于登出时清理）
  static Future<void> clearLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_usersStorageKey);
      debugPrint('用户本地存储已清除');
    } catch (e) {
      debugPrint('清除用户本地存储失败: $e');
    }
  }

  /// 用户登录后初始化缓存
  Future<void> initializeAfterLogin() async {
    debugPrint('用户登录后初始化用户服务缓存');
    await _loadUsersFromLocal();
  }

  /// 释放资源
  void dispose() {
    _authService.currentUserNotifier.removeListener(_onAuthStateChanged);
  }

  /// 刷新缓存
  Future<void> refreshCache() async {
    clearCache();
    await getAllUsers();
  }
}
