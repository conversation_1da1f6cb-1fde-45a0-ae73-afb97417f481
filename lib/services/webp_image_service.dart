import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:flutter_image_compress/flutter_image_compress.dart';
import '../config/r2_config.dart';

/// 图片处理服务
///
/// 功能：
/// - WebP格式编码和压缩（使用flutter_image_compress）
/// - JPEG格式编码和压缩（备选方案）
/// - 智能压缩算法
/// - 尺寸调整和优化
/// - 质量优化
class WebPImageService {
  /// 支持WebP的平台检查
  static bool get isWebPSupported {
    // Web平台和移动平台都支持WebP
    return kIsWeb || Platform.isAndroid || Platform.isIOS;
  }

  /// 处理图片并转换为WebP格式
  ///
  /// [imageBytes] 原始图片字节
  /// [quality] 压缩质量 (0-100)
  /// [maxWidth] 最大宽度
  /// [maxHeight] 最大高度
  /// [forThumbnail] 是否为缩略图
  static Future<ProcessedImageResult> processImage({
    required Uint8List imageBytes,
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
    bool forThumbnail = false,
  }) async {
    try {
      debugPrint('🔄 [WebP处理] 开始处理图片');
      debugPrint('🔄 [WebP处理] 原始大小: ${imageBytes.length} bytes');
      debugPrint('🔄 [WebP处理] 质量设置: $quality');
      debugPrint('🔄 [WebP处理] 是否缩略图: $forThumbnail');

      // 创建临时文件
      final tempDir = Directory.systemTemp;
      final tempFile = File('${tempDir.path}/temp_image_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await tempFile.writeAsBytes(imageBytes);

      try {
        // 计算目标尺寸
        final originalImage = img.decodeImage(imageBytes);
        if (originalImage == null) {
          throw Exception('无法解码图片');
        }

        debugPrint('🔄 [WebP处理] 原始尺寸: ${originalImage.width}x${originalImage.height}');

        final targetSize = _calculateTargetSize(
          originalImage.width,
          originalImage.height,
          maxWidth ?? (forThumbnail ? 300 : R2Config.maxImageWidth),
          maxHeight ?? (forThumbnail ? 300 : R2Config.maxImageHeight),
        );

        debugPrint('🔄 [WebP处理] 目标尺寸: ${targetSize.width}x${targetSize.height}');

        // 智能质量调整
        final adjustedQuality = _calculateOptimalQuality(
          originalImage,
          quality,
          forThumbnail,
        );

        debugPrint('🔄 [WebP处理] 调整后质量: $adjustedQuality');

        // 尝试使用WebP压缩
        Uint8List? compressedBytes;
        String format = 'JPEG'; // 默认格式
        String extension = 'jpg'; // 默认扩展名

        try {
          // 使用flutter_image_compress压缩为WebP
          final webpResult = await FlutterImageCompress.compressWithFile(
            tempFile.path,
            quality: adjustedQuality,
            minWidth: targetSize.width,
            minHeight: targetSize.height,
            format: CompressFormat.webp,
          );

          if (webpResult != null) {
            compressedBytes = webpResult;
            format = 'WebP';
            extension = 'webp';
            debugPrint('✅ [WebP处理] 成功使用WebP格式');
          }
        } catch (e) {
          debugPrint('⚠️ [WebP处理] WebP压缩失败，尝试JPEG: $e');
        }

        // 如果WebP失败，使用JPEG作为备选
        if (compressedBytes == null) {
          final jpegResult = await FlutterImageCompress.compressWithFile(
            tempFile.path,
            quality: adjustedQuality,
            minWidth: targetSize.width,
            minHeight: targetSize.height,
            format: CompressFormat.jpeg,
          );

          if (jpegResult != null) {
            compressedBytes = jpegResult;
            format = 'JPEG';
            extension = 'jpg';
            debugPrint('🔄 [WebP处理] 使用JPEG格式作为备选');
          } else {
            throw Exception('图片压缩失败');
          }
        }

        final compressionRatio = (1 - compressedBytes.length / imageBytes.length) * 100;

        debugPrint('✅ [WebP处理] 处理完成');
        debugPrint('✅ [WebP处理] 输出格式: $format');
        debugPrint('✅ [WebP处理] 压缩后大小: ${compressedBytes.length} bytes');
        debugPrint('✅ [WebP处理] 压缩率: ${compressionRatio.toStringAsFixed(1)}%');

        return ProcessedImageResult(
          imageBytes: compressedBytes,
          width: targetSize.width,
          height: targetSize.height,
          format: format,
          extension: extension,
          quality: adjustedQuality,
          originalSize: imageBytes.length,
          compressedSize: compressedBytes.length,
          compressionRatio: compressionRatio,
        );
      } finally {
        // 清理临时文件
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }
    } catch (e) {
      debugPrint('❌ [WebP处理] 处理失败: $e');
      rethrow;
    }
  }

  /// 计算目标尺寸
  static ImageSize _calculateTargetSize(
    int originalWidth,
    int originalHeight,
    int maxWidth,
    int maxHeight,
  ) {
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return ImageSize(originalWidth, originalHeight);
    }

    final widthRatio = maxWidth / originalWidth;
    final heightRatio = maxHeight / originalHeight;
    final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

    return ImageSize(
      (originalWidth * ratio).round(),
      (originalHeight * ratio).round(),
    );
  }

  /// 智能质量计算
  static int _calculateOptimalQuality(
    img.Image image,
    int baseQuality,
    bool forThumbnail,
  ) {
    // 缩略图使用较低质量
    if (forThumbnail) {
      return (baseQuality * 0.8).round().clamp(60, 85);
    }

    // 根据图片尺寸调整质量
    final pixelCount = image.width * image.height;
    
    if (pixelCount > 2000000) { // 大于200万像素
      return (baseQuality * 0.9).round().clamp(75, 90);
    } else if (pixelCount > 1000000) { // 大于100万像素
      return baseQuality.clamp(80, 95);
    } else {
      return (baseQuality * 1.1).round().clamp(85, 100);
    }
  }

  /// 批量处理图片
  static Future<List<ProcessedImageResult>> processImages({
    required List<Uint8List> imageBytesList,
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
    Function(int current, int total)? onProgress,
  }) async {
    final results = <ProcessedImageResult>[];
    
    for (int i = 0; i < imageBytesList.length; i++) {
      onProgress?.call(i + 1, imageBytesList.length);
      
      final result = await processImage(
        imageBytes: imageBytesList[i],
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );
      
      results.add(result);
    }
    
    return results;
  }
}

/// 图片尺寸类
class ImageSize {
  final int width;
  final int height;

  ImageSize(this.width, this.height);
}

/// 处理后的图片结果
class ProcessedImageResult {
  /// 处理后的图片字节
  final Uint8List imageBytes;
  
  /// 图片宽度
  final int width;
  
  /// 图片高度
  final int height;
  
  /// 图片格式
  final String format;
  
  /// 文件扩展名
  final String extension;
  
  /// 压缩质量
  final int quality;
  
  /// 原始文件大小
  final int originalSize;
  
  /// 压缩后文件大小
  final int compressedSize;
  
  /// 压缩率（百分比）
  final double compressionRatio;

  ProcessedImageResult({
    required this.imageBytes,
    required this.width,
    required this.height,
    required this.format,
    required this.extension,
    required this.quality,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
  });

  /// 获取格式化的文件大小
  String get formattedOriginalSize => _formatFileSize(originalSize);
  String get formattedCompressedSize => _formatFileSize(compressedSize);

  /// 格式化文件大小
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
