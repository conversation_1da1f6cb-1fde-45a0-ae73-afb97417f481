import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import 'service_locator.dart';

/// 钓点互动服务
///
/// 负责处理钓点的点赞、倒赞、收藏等互动功能
/// 使用 user_likes 和 user_unlikes 表存储用户互动记录
class SpotInteractionService {
  static final SpotInteractionService _instance =
      SpotInteractionService._internal();
  factory SpotInteractionService() => _instance;
  SpotInteractionService._internal();

  final _pb = PocketBaseConfig.instance.client;

  // 缓存用户的点赞和倒赞记录
  final Set<String> _userLikes = {};
  final Set<String> _userUnlikes = {};
  String? _cachedUserId;

  /// 加载用户的所有互动记录
  /// 这个方法会一次性加载用户的所有点赞和倒赞记录，减少网络请求
  Future<void> loadUserInteractions() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [互动加载] 用户未登录');
        return;
      }

      final userId = currentUser.id;

      // 如果已经缓存了当前用户的数据，直接返回
      if (_cachedUserId == userId) {
        debugPrint('✅ [互动加载] 使用缓存的互动数据');
        return;
      }

      debugPrint('🔍 [互动加载] 开始加载用户互动记录: $userId');

      // 并行加载点赞和倒赞记录
      final futures = await Future.wait([
        _pb.collection('user_likes').getFullList(filter: 'user_id = "$userId"'),
        _pb
            .collection('user_unlikes')
            .getFullList(filter: 'user_id = "$userId"'),
      ]);

      final likeRecords = futures[0];
      final unlikeRecords = futures[1];

      // 清空旧缓存
      _userLikes.clear();
      _userUnlikes.clear();

      // 填充新缓存
      for (final record in likeRecords) {
        _userLikes.add(record.data['spot_id'] as String);
      }

      for (final record in unlikeRecords) {
        _userUnlikes.add(record.data['spot_id'] as String);
      }

      _cachedUserId = userId;

      debugPrint(
        '✅ [互动加载] 加载完成: 点赞${_userLikes.length}个, 倒赞${_userUnlikes.length}个',
      );
    } catch (e) {
      debugPrint('❌ [互动加载] 加载用户互动记录失败: $e');
    }
  }

  /// 获取用户点赞的钓点ID集合
  Set<String> get userLikes {
    return Set.from(_userLikes);
  }

  /// 获取用户倒赞的钓点ID集合
  Set<String> get userUnlikes {
    return Set.from(_userUnlikes);
  }

  /// 检查用户是否点赞了指定钓点
  bool isLiked(String spotId) {
    return _userLikes.contains(spotId);
  }

  /// 检查用户是否倒赞了指定钓点
  bool isUnliked(String spotId) {
    return _userUnlikes.contains(spotId);
  }

  /// 点赞钓点
  /// [spotId] 钓点ID
  /// 返回是否成功
  Future<bool> likeSpot(String spotId) async {
    try {
      // 检查用户是否已登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [点赞] 用户未登录');
        return false;
      }

      final userId = currentUser.id;
      debugPrint('🔍 [点赞] 开始点赞钓点: $spotId, 用户: $userId');

      // 确保已加载用户互动记录
      await loadUserInteractions();

      // 检查是否已经点赞过
      if (_userLikes.contains(spotId)) {
        debugPrint('⚠️ [点赞] 用户已经点赞过此钓点');
        return false;
      }

      // 原子操作：添加点赞记录，删除倒赞记录（如果存在），更新统计
      bool success = await _performAtomicLikeOperation(spotId, userId);

      if (success) {
        // 更新本地缓存
        _userLikes.add(spotId);
        _userUnlikes.remove(spotId);
        debugPrint('✅ [点赞] 点赞成功');
      }

      return success;
    } catch (e) {
      debugPrint('❌ [点赞] 点赞失败: $e');
      return false;
    }
  }

  /// 执行原子化的点赞操作
  Future<bool> _performAtomicLikeOperation(String spotId, String userId) async {
    try {
      // 1. 添加到 user_likes 表
      await _pb
          .collection('user_likes')
          .create(body: {'user_id': userId, 'spot_id': spotId});

      // 2. 从 user_unlikes 表删除（如果存在）
      try {
        final existingUnlike = await _pb
            .collection('user_unlikes')
            .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');
        await _pb.collection('user_unlikes').delete(existingUnlike.id);
        debugPrint('🔍 [点赞] 删除了之前的倒赞记录');
      } catch (e) {
        // 没有倒赞记录，忽略错误
      }

      // 3. 更新钓点统计
      await _updateSpotCounts(spotId);

      return true;
    } catch (e) {
      debugPrint('❌ [点赞] 原子操作失败: $e');
      return false;
    }
  }

  /// 倒赞钓点
  /// [spotId] 钓点ID
  /// 返回是否成功
  Future<bool> unlikeSpot(String spotId) async {
    try {
      // 检查用户是否已登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [倒赞] 用户未登录');
        return false;
      }

      final userId = currentUser.id;
      debugPrint('🔍 [倒赞] 开始倒赞钓点: $spotId, 用户: $userId');

      // 确保已加载用户互动记录
      await loadUserInteractions();

      // 检查是否已经倒赞过
      if (_userUnlikes.contains(spotId)) {
        debugPrint('⚠️ [倒赞] 用户已经倒赞过此钓点');
        return false;
      }

      // 原子操作：添加倒赞记录，删除点赞记录（如果存在），更新统计
      bool success = await _performAtomicUnlikeOperation(spotId, userId);

      if (success) {
        // 更新本地缓存
        _userUnlikes.add(spotId);
        _userLikes.remove(spotId);
        debugPrint('✅ [倒赞] 倒赞成功');
      }

      return success;
    } catch (e) {
      debugPrint('❌ [倒赞] 倒赞失败: $e');
      return false;
    }
  }

  /// 执行原子化的倒赞操作
  Future<bool> _performAtomicUnlikeOperation(
    String spotId,
    String userId,
  ) async {
    try {
      // 1. 添加到 user_unlikes 表
      await _pb
          .collection('user_unlikes')
          .create(body: {'user_id': userId, 'spot_id': spotId});

      // 2. 从 user_likes 表删除（如果存在）
      try {
        final existingLike = await _pb
            .collection('user_likes')
            .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');
        await _pb.collection('user_likes').delete(existingLike.id);
        debugPrint('🔍 [倒赞] 删除了之前的点赞记录');
      } catch (e) {
        // 没有点赞记录，忽略错误
      }

      // 3. 更新钓点统计
      await _updateSpotCounts(spotId);

      return true;
    } catch (e) {
      debugPrint('❌ [倒赞] 原子操作失败: $e');
      return false;
    }
  }

  /// 取消点赞
  /// [spotId] 钓点ID
  /// 返回是否成功
  Future<bool> cancelLike(String spotId) async {
    try {
      // 检查用户是否已登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [取消点赞] 用户未登录');
        return false;
      }

      final userId = currentUser.id;
      debugPrint('🔍 [取消点赞] 开始取消点赞: $spotId, 用户: $userId');

      // 确保已加载用户互动记录
      await loadUserInteractions();

      // 检查是否已经点赞
      if (!_userLikes.contains(spotId)) {
        debugPrint('⚠️ [取消点赞] 用户没有点赞此钓点');
        return false;
      }

      // 删除点赞记录
      try {
        final existingLike = await _pb
            .collection('user_likes')
            .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');
        await _pb.collection('user_likes').delete(existingLike.id);

        // 更新钓点统计
        await _updateSpotCounts(spotId);

        // 更新本地缓存
        _userLikes.remove(spotId);

        debugPrint('✅ [取消点赞] 取消点赞成功');
        return true;
      } catch (e) {
        debugPrint('❌ [取消点赞] 删除点赞记录失败: $e');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [取消点赞] 取消点赞失败: $e');
      return false;
    }
  }

  /// 取消倒赞
  /// [spotId] 钓点ID
  /// 返回是否成功
  Future<bool> cancelUnlike(String spotId) async {
    try {
      // 检查用户是否已登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [取消倒赞] 用户未登录');
        return false;
      }

      final userId = currentUser.id;
      debugPrint('🔍 [取消倒赞] 开始取消倒赞: $spotId, 用户: $userId');

      // 确保已加载用户互动记录
      await loadUserInteractions();

      // 检查是否已经倒赞
      if (!_userUnlikes.contains(spotId)) {
        debugPrint('⚠️ [取消倒赞] 用户没有倒赞此钓点');
        return false;
      }

      // 删除倒赞记录
      try {
        final existingUnlike = await _pb
            .collection('user_unlikes')
            .getFirstListItem('user_id = "$userId" && spot_id = "$spotId"');
        await _pb.collection('user_unlikes').delete(existingUnlike.id);

        // 更新钓点统计
        await _updateSpotCounts(spotId);

        // 更新本地缓存
        _userUnlikes.remove(spotId);

        debugPrint('✅ [取消倒赞] 取消倒赞成功');
        return true;
      } catch (e) {
        debugPrint('❌ [取消倒赞] 删除倒赞记录失败: $e');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [取消倒赞] 取消倒赞失败: $e');
      return false;
    }
  }

  /// 获取用户对钓点的互动状态
  /// [spotId] 钓点ID
  /// 返回互动状态 {'isLiked': bool, 'isUnliked': bool}
  Future<Map<String, bool>> getUserInteractionStatus(String spotId) async {
    try {
      // 检查用户是否已登录
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        return {'isLiked': false, 'isUnliked': false};
      }

      debugPrint('🔍 [互动状态] 获取用户互动状态: $spotId');

      // 确保已加载用户互动记录
      await loadUserInteractions();

      // 从缓存中获取状态
      final isLiked = _userLikes.contains(spotId);
      final isUnliked = _userUnlikes.contains(spotId);

      debugPrint('🔍 [互动状态] 状态: 点赞=$isLiked, 倒赞=$isUnliked');
      return {'isLiked': isLiked, 'isUnliked': isUnliked};
    } catch (e) {
      debugPrint('❌ [互动状态] 获取互动状态失败: $e');
      return {'isLiked': false, 'isUnliked': false};
    }
  }

  /// 更新钓点的点赞数统计
  /// [spotId] 钓点ID
  Future<void> _updateSpotCounts(String spotId) async {
    try {
      debugPrint('🔍 [更新统计] 开始更新钓点点赞数: $spotId');

      // 首先检查钓点是否存在
      try {
        await _pb.collection('fishing_spots').getOne(spotId);
      } catch (e) {
        debugPrint('❌ [更新统计] 钓点不存在: $spotId, 错误: $e');
        return;
      }

      // 统计点赞数
      final likeRecords = await _pb
          .collection('user_likes')
          .getFullList(filter: 'spot_id = "$spotId"');

      // 统计倒赞数
      final unlikeRecords = await _pb
          .collection('user_unlikes')
          .getFullList(filter: 'spot_id = "$spotId"');

      final likeCount = likeRecords.length;
      final unlikeCount = unlikeRecords.length;

      debugPrint('🔍 [更新统计] 点赞数: $likeCount, 倒赞数: $unlikeCount');

      // 更新钓点记录
      try {
        await _pb
            .collection('fishing_spots')
            .update(spotId, body: {'likes': likeCount, 'unlikes': unlikeCount});
        debugPrint('✅ [更新统计] 钓点统计更新成功');
      } catch (e) {
        debugPrint('❌ [更新统计] 更新钓点记录失败: $spotId, 错误: $e');
        // 如果是404错误，说明钓点已被删除，清理相关的点赞记录
        if (e.toString().contains('404')) {
          debugPrint('🧹 [更新统计] 钓点已被删除，清理相关点赞记录');
          await _cleanupOrphanedLikes(spotId);
        }
      }
    } catch (e) {
      debugPrint('❌ [更新统计] 更新钓点统计失败: $e');
    }
  }

  /// 清理孤立的点赞记录
  /// [spotId] 钓点ID
  Future<void> _cleanupOrphanedLikes(String spotId) async {
    try {
      // 删除相关的点赞记录
      final likeRecords = await _pb
          .collection('user_likes')
          .getFullList(filter: 'spot_id = "$spotId"');

      for (final record in likeRecords) {
        try {
          await _pb.collection('user_likes').delete(record.id);
        } catch (e) {
          debugPrint('❌ [清理] 删除点赞记录失败: ${record.id}, 错误: $e');
        }
      }

      // 删除相关的倒赞记录
      final unlikeRecords = await _pb
          .collection('user_unlikes')
          .getFullList(filter: 'spot_id = "$spotId"');

      for (final record in unlikeRecords) {
        try {
          await _pb.collection('user_unlikes').delete(record.id);
        } catch (e) {
          debugPrint('❌ [清理] 删除倒赞记录失败: ${record.id}, 错误: $e');
        }
      }

      // 从本地缓存中移除
      _userLikes.remove(spotId);
      _userUnlikes.remove(spotId);

      debugPrint('✅ [清理] 孤立的点赞记录清理完成: $spotId');
    } catch (e) {
      debugPrint('❌ [清理] 清理孤立点赞记录失败: $e');
    }
  }
}
