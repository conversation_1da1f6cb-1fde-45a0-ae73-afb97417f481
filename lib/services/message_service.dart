import 'dart:async';
import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import '../models/message/conversation.dart';
import '../models/message/message.dart';
import '../models/user.dart' as user_model;
import 'auth_service_new.dart';

/// 消息服务
///
/// 使用定时轮询+手动刷新的方式更新消息，不使用实时订阅
class MessageService {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  final AuthService _authService = AuthService();
  final _pb = PocketBaseConfig.instance.client;

  // 轮询定时器
  Timer? _pollingTimer;

  // 轮询间隔（30秒）
  static const Duration _pollingInterval = Duration(seconds: 30);

  // 数据缓存
  List<Conversation> _conversationsCache = [];
  final Map<String, List<Message>> _messagesCache = {};

  // 状态通知
  final ValueNotifier<List<Conversation>> _conversationsNotifier =
      ValueNotifier<List<Conversation>>([]);
  final ValueNotifier<int> _unreadCountNotifier = ValueNotifier<int>(0);

  // 是否正在轮询
  bool _isPolling = false;

  /// 获取当前用户
  user_model.User? get currentUser => _authService.currentUser;

  /// 对话列表通知器
  ValueNotifier<List<Conversation>> get conversationsNotifier =>
      _conversationsNotifier;

  /// 未读消息数通知器
  ValueNotifier<int> get unreadCountNotifier => _unreadCountNotifier;

  /// 是否正在轮询
  bool get isPolling => _isPolling;

  /// 初始化服务
  Future<void> initialize() async {
    debugPrint('📱 [消息服务] 初始化消息服务');

    // 监听认证状态变化
    _authService.currentUserNotifier.addListener(_onAuthStateChanged);

    // 不在初始化时自动开始轮询，只在消息页面打开时才开始
    debugPrint('📱 [消息服务] 消息服务初始化完成，等待手动启动轮询');
  }

  /// 认证状态变化处理
  void _onAuthStateChanged() {
    if (currentUser != null) {
      // 用户登录，但不自动开始轮询，等待消息页面手动启动
      debugPrint('📱 [消息服务] 用户已登录，等待消息页面启动轮询');
    } else {
      // 用户登出，停止轮询并清除缓存
      stopPolling();
      _clearCache();
    }
  }

  /// 开始轮询
  Future<void> startPolling() async {
    if (_isPolling || currentUser == null) return;

    debugPrint('🔄 [消息服务] 开始消息轮询，间隔: ${_pollingInterval.inSeconds}秒');
    _isPolling = true;

    // 立即执行一次
    await _pollMessages();

    // 启动定时器
    _pollingTimer = Timer.periodic(_pollingInterval, (timer) async {
      if (currentUser == null) {
        timer.cancel();
        _isPolling = false;
        return;
      }

      await _pollMessages();
    });
  }

  /// 停止轮询
  void stopPolling() {
    debugPrint('⏹️ [消息服务] 停止消息轮询');
    _pollingTimer?.cancel();
    _pollingTimer = null;
    _isPolling = false;
  }

  /// 轮询消息
  Future<void> _pollMessages() async {
    try {
      await _fetchConversations();
      await _updateUnreadCount();
    } catch (e) {
      debugPrint('❌ [消息服务] 轮询消息失败: $e');
    }
  }

  /// 手动刷新消息
  Future<void> refreshMessages() async {
    debugPrint('🔄 [消息服务] 手动刷新消息');
    await _pollMessages();
  }

  /// 获取对话列表
  Future<List<Conversation>> getConversations() async {
    if (_conversationsCache.isEmpty) {
      await _fetchConversations();
    }
    return _conversationsCache;
  }

  /// 从服务器获取对话列表
  Future<void> _fetchConversations() async {
    final user = currentUser;
    if (user == null) return;

    try {
      // 查询用户参与的所有对话
      final records = await _pb
          .collection('conversations')
          .getList(
            filter:
                'user1.id = "${user.id}" || user2.id = "${user.id}"',
            sort: '-last_message_time',
            expand: 'user1,user2,last_message',
          );

      final conversations = <Conversation>[];

      for (final record in records.items) {
        try {
          final conversation = _parseConversation(record, user.id);
          if (conversation != null) {
            conversations.add(conversation);
          }
        } catch (e) {
          debugPrint('❌ [消息服务] 解析对话失败: $e');
        }
      }

      _conversationsCache = conversations;
      _conversationsNotifier.value = conversations;

      debugPrint('✅ [消息服务] 获取到 ${conversations.length} 个对话');
    } catch (e) {
      debugPrint('❌ [消息服务] 获取对话列表失败: $e');
    }
  }

  /// 解析对话记录
  Conversation? _parseConversation(dynamic record, String currentUserId) {
    try {
      final participantA = record.expand?['user1'];
      final participantB = record.expand?['user2'];
      final lastMessage = record.expand?['last_message'];

      // 确定对方用户
      final isUserA = participantA?['id'] == currentUserId;
      final otherUser = isUserA ? participantB : participantA;

      if (otherUser == null) return null;

      // 获取未读数
      final unreadCount =
          isUserA
              ? (record.data['user1_unread_count'] ?? 0) as int
              : (record.data['user2_unread_count'] ?? 0) as int;

      return Conversation(
        id: record.id,
        otherUserId: otherUser['id'],
        otherUserName: otherUser['name'] ?? otherUser['username'] ?? '未知用户',
        otherUserAvatar:
            otherUser['avatar'] != null
                ? _pb.files.getUrl(otherUser, otherUser['avatar']).toString()
                : null,
        lastMessage: lastMessage?['content'],
        lastMessageTime:
            lastMessage != null
                ? DateTime.parse(lastMessage['created'])
                : DateTime.parse(record.created),
        lastMessageType: lastMessage?['message_type'] ?? 'text',
        unreadCount: unreadCount,
        isPending: record.data['status'] == 'pending',
      );
    } catch (e) {
      debugPrint('❌ [消息服务] 解析对话记录失败: $e');
      return null;
    }
  }

  /// 更新未读消息总数
  Future<void> _updateUnreadCount() async {
    final totalUnread = _conversationsCache.fold<int>(
      0,
      (sum, conv) => sum + conv.unreadCount,
    );
    _unreadCountNotifier.value = totalUnread;
  }

  /// 获取与指定用户的消息列表
  Future<List<Message>> getMessages(String conversationId) async {
    if (_messagesCache.containsKey(conversationId)) {
      return _messagesCache[conversationId]!;
    }

    return await _fetchMessages(conversationId);
  }

  /// 从服务器获取消息列表
  Future<List<Message>> _fetchMessages(String conversationId) async {
    try {
      final records = await _pb
          .collection('messages')
          .getList(
            filter: 'conversation_id = "$conversationId"',
            sort: 'created',
            expand: 'sender,receiver',
          );

      final messages = <Message>[];

      for (final record in records.items) {
        try {
          final message = _parseMessage(record);
          if (message != null) {
            messages.add(message);
          }
        } catch (e) {
          debugPrint('❌ [消息服务] 解析消息失败: $e');
        }
      }

      _messagesCache[conversationId] = messages;
      return messages;
    } catch (e) {
      debugPrint('❌ [消息服务] 获取消息列表失败: $e');
      return [];
    }
  }

  /// 解析消息记录
  Message? _parseMessage(dynamic record) {
    try {
      final sender = record.expand?['sender'];
      // final receiver = record.expand?['receiver']; // 暂时不使用

      return Message(
        id: record.id,
        senderId: record.data['sender'],
        receiverId: record.data['receiver'],
        content: record.data['content'] ?? '',
        messageType: record.data['message_type'] ?? 'text',
        status: record.data['status'] ?? 'sent',
        isRead: record.data['is_read'] ?? false,
        createdAt: DateTime.parse(record.created),
        senderName: sender?['name'] ?? sender?['username'] ?? '未知用户',
        senderAvatar:
            sender?['avatar'] != null
                ? _pb.files.getUrl(sender, sender['avatar']).toString()
                : null,
      );
    } catch (e) {
      debugPrint('❌ [消息服务] 解析消息记录失败: $e');
      return null;
    }
  }

  /// 发送消息
  Future<Message?> sendMessage(
    String receiverId,
    String content, {
    String type = 'text',
  }) async {
    final user = currentUser;
    if (user == null) return null;

    try {
      // 查找或创建对话
      final conversationId = await _findOrCreateConversation(receiverId);
      if (conversationId == null) return null;

      // 创建消息记录
      final record = await _pb
          .collection('messages')
          .create(
            body: {
              'conversation_id': conversationId,
              'sender': user.id,
              'receiver': receiverId,
              'content': content,
              'message_type': type,
              'status': 'sent',
              'is_read': false,
            },
          );

      // 更新对话的最后消息
      await _pb
          .collection('conversations')
          .update(
            conversationId,
            body: {
              'last_message': record.id,
              'last_message_time': DateTime.now().toIso8601String(),
              'user2_unread_count+': 1, // 增加接收方未读数
            },
          );

      // 清除缓存，触发刷新
      _messagesCache.remove(conversationId);
      await _fetchConversations();

      debugPrint('✅ [消息服务] 消息发送成功');
      return _parseMessage(record);
    } catch (e) {
      debugPrint('❌ [消息服务] 发送消息失败: $e');
      return null;
    }
  }

  /// 查找或创建对话
  Future<String?> _findOrCreateConversation(String otherUserId) async {
    final user = currentUser;
    if (user == null) return null;

    try {
      // 先查找现有对话
      final existing = await _pb
          .collection('conversations')
          .getList(
            filter:
                '(user1.id = "${user.id}" && user2.id = "$otherUserId") || (user1.id = "$otherUserId" && user2.id = "${user.id}")',
          );

      if (existing.items.isNotEmpty) {
        return existing.items.first.id;
      }

      // 创建新对话
      final record = await _pb
          .collection('conversations')
          .create(
            body: {
              'user1': user.id,
              'user2': otherUserId,
              'user1_unread_count': 0,
              'user2_unread_count': 0,
              'is_active': true,
            },
          );

      return record.id;
    } catch (e) {
      debugPrint('❌ [消息服务] 查找或创建对话失败: $e');
      return null;
    }
  }

  /// 标记消息为已读
  Future<void> markAsRead(String conversationId) async {
    final user = currentUser;
    if (user == null) return;

    try {
      // 获取对话信息
      final conversation = await _pb
          .collection('conversations')
          .getOne(conversationId);

      // 确定当前用户是user1还是user2
      final isUser1 = conversation.data['user1'] == user.id;
      final unreadField =
          isUser1 ? 'user1_unread_count' : 'user2_unread_count';

      // 重置未读数
      await _pb
          .collection('conversations')
          .update(conversationId, body: {unreadField: 0});

      // 标记该对话中的消息为已读
      await _pb
          .collection('messages')
          .getList(
            filter:
                'conversation_id = "$conversationId" && receiver = "${user.id}" && is_read = false',
          )
          .then((records) async {
            for (final record in records.items) {
              await _pb
                  .collection('messages')
                  .update(record.id, body: {'is_read': true});
            }
          });

      // 刷新数据
      await _fetchConversations();

      debugPrint('✅ [消息服务] 标记消息为已读');
    } catch (e) {
      debugPrint('❌ [消息服务] 标记消息为已读失败: $e');
    }
  }

  /// 清除缓存
  void _clearCache() {
    _conversationsCache.clear();
    _messagesCache.clear();
    _conversationsNotifier.value = [];
    _unreadCountNotifier.value = 0;
  }

  /// 销毁服务
  void dispose() {
    stopPolling();
    _authService.currentUserNotifier.removeListener(_onAuthStateChanged);
    _conversationsNotifier.dispose();
    _unreadCountNotifier.dispose();
  }
}
