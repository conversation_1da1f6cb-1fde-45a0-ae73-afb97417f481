import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import 'service_locator.dart';

/// 用户统计数据模型
class UserStats {
  final int points; // 漂豆（积分）
  final int spotsCount; // 钓点数量
  final int postsCount; // 动态数量
  final int newRepliesCount; // 最新回复数量

  UserStats({
    required this.points,
    required this.spotsCount,
    required this.postsCount,
    required this.newRepliesCount,
  });
}

/// 用户统计数据服务
class UserStatsService {
  final _pb = PocketBaseConfig.instance.client;

  /// 获取用户统计数据
  Future<UserStats> getUserStats() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        return UserStats(
          points: 0,
          spotsCount: 0,
          postsCount: 0,
          newRepliesCount: 0,
        );
      }

      final userId = currentUser.id;

      // 并行获取各项统计数据
      final results = await Future.wait([
        _getUserPoints(userId),
        _getUserSpotsCount(userId),
        _getUserPostsCount(userId),
        _getNewRepliesCount(userId),
      ]);

      return UserStats(
        points: results[0],
        spotsCount: results[1],
        postsCount: results[2],
        newRepliesCount: results[3],
      );
    } catch (e) {
      debugPrint('获取用户统计数据失败: $e');
      return UserStats(
        points: 0,
        spotsCount: 0,
        postsCount: 0,
        newRepliesCount: 0,
      );
    }
  }

  /// 获取用户积分
  Future<int> _getUserPoints(String userId) async {
    try {
      final user = await _pb.collection('users').getOne(userId);
      return user.data['points'] ?? 0;
    } catch (e) {
      debugPrint('获取用户积分失败: $e');
      return 0;
    }
  }

  /// 获取用户发布的钓点数量
  Future<int> _getUserSpotsCount(String userId) async {
    try {
      final result = await _pb
          .collection('fishing_spots')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"');
      return result.totalItems;
    } catch (e) {
      debugPrint('获取用户钓点数量失败: $e');
      return 0;
    }
  }

  /// 获取用户发布的动态数量
  Future<int> _getUserPostsCount(String userId) async {
    try {
      // 假设有一个 posts 集合存储用户动态
      final result = await _pb
          .collection('posts')
          .getList(page: 1, perPage: 1, filter: 'user_id = "$userId"');
      return result.totalItems;
    } catch (e) {
      // 如果 posts 集合不存在，直接返回 0，不打印错误日志
      if (e.toString().contains('Missing collection context')) {
        return 0;
      }
      debugPrint('获取用户动态数量失败: $e');
      return 0;
    }
  }

  /// 获取用户收到的新回复数量
  Future<int> _getNewRepliesCount(String userId) async {
    try {
      // 首先检查 comments 集合是否存在
      try {
        await _pb.collection('comments').getList(page: 1, perPage: 1);
      } catch (e) {
        if (e.toString().contains('Missing collection context')) {
          // comments 集合不存在，直接返回 0
          return 0;
        }
      }

      // 获取用户发布的钓点ID列表
      final userSpots = await _pb
          .collection('fishing_spots')
          .getFullList(filter: 'user_id = "$userId"');

      // 获取用户发布的动态ID列表
      List<dynamic> userPosts = [];
      try {
        userPosts = await _pb
            .collection('posts')
            .getFullList(filter: 'user_id = "$userId"');
      } catch (e) {
        // posts 集合可能不存在，忽略错误（不打印日志）
        if (!e.toString().contains('Missing collection context')) {
          debugPrint('posts 集合查询失败: $e');
        }
      }

      int newRepliesCount = 0;

      // 统计钓点下的新回复
      for (final spot in userSpots) {
        try {
          final replies = await _pb
              .collection('comments')
              .getList(
                page: 1,
                perPage: 50,
                filter:
                    'spot_id = "${spot.id}" && user_id != "$userId" && is_read = false',
                sort: '-created',
              );
          newRepliesCount += replies.totalItems;
        } catch (e) {
          // 如果是集合不存在的错误，不打印日志
          if (!e.toString().contains('Missing collection context')) {
            debugPrint('获取钓点回复失败: $e');
          }
        }
      }

      // 统计动态下的新回复
      for (final post in userPosts) {
        try {
          final replies = await _pb
              .collection('post_comments')
              .getList(
                page: 1,
                perPage: 50,
                filter:
                    'post_id = "${post.id}" && user_id != "$userId" && is_read = false',
                sort: '-created',
              );
          newRepliesCount += replies.totalItems;
        } catch (e) {
          // 如果是集合不存在的错误，不打印日志
          if (!e.toString().contains('Missing collection context')) {
            debugPrint('获取动态回复失败: $e');
          }
        }
      }

      return newRepliesCount;
    } catch (e) {
      debugPrint('获取新回复数量失败: $e');
      return 0;
    }
  }

  /// 获取用户的新回复列表（用于跳转到具体回复）
  Future<List<Map<String, dynamic>>> getNewRepliesList() async {
    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) return [];

      // 首先检查 comments 集合是否存在
      try {
        await _pb.collection('comments').getList(page: 1, perPage: 1);
      } catch (e) {
        if (e.toString().contains('Missing collection context')) {
          // comments 集合不存在，直接返回空列表
          return [];
        }
      }

      final userId = currentUser.id;
      final List<Map<String, dynamic>> newReplies = [];

      // 获取用户发布的钓点
      final userSpots = await _pb
          .collection('fishing_spots')
          .getFullList(filter: 'user_id = "$userId"');

      // 获取钓点下的新回复
      for (final spot in userSpots) {
        try {
          final replies = await _pb
              .collection('comments')
              .getList(
                page: 1,
                perPage: 20,
                filter:
                    'spot_id = "${spot.id}" && user_id != "$userId" && is_read = false',
                sort: '-created',
                expand: 'user_id',
              );

          for (final reply in replies.items) {
            newReplies.add({
              'id': reply.id,
              'type': 'spot_comment',
              'content': reply.data['content'] ?? '',
              'created':
                  reply.data['created'] ?? DateTime.now().toIso8601String(),
              'user_name': reply.get<String>('expand.user_id.name'),
              'spot_id': spot.id,
              'spot_name': spot.data['name'] ?? '未知钓点',
            });
          }
        } catch (e) {
          // 如果是集合不存在的错误，不打印日志
          if (!e.toString().contains('Missing collection context')) {
            debugPrint('获取钓点回复详情失败: $e');
          }
        }
      }

      // 获取用户发布的动态
      try {
        final userPosts = await _pb
            .collection('posts')
            .getFullList(filter: 'user_id = "$userId"');

        // 获取动态下的新回复
        for (final post in userPosts) {
          try {
            final replies = await _pb
                .collection('post_comments')
                .getList(
                  page: 1,
                  perPage: 20,
                  filter:
                      'post_id = "${post.id}" && user_id != "$userId" && is_read = false',
                  sort: '-created',
                  expand: 'user_id',
                );

            for (final reply in replies.items) {
              newReplies.add({
                'id': reply.id,
                'type': 'post_comment',
                'content': reply.data['content'] ?? '',
                'created':
                    reply.data['created'] ?? DateTime.now().toIso8601String(),
                'user_name': reply.get<String>('expand.user_id.name'),
                'post_id': post.id,
                'post_title': post.data['title'] ?? '动态',
              });
            }
          } catch (e) {
            // 如果是集合不存在的错误，不打印日志
            if (!e.toString().contains('Missing collection context')) {
              debugPrint('获取动态回复详情失败: $e');
            }
          }
        }
      } catch (e) {
        // 如果是集合不存在的错误，不打印日志
        if (!e.toString().contains('Missing collection context')) {
          debugPrint('posts 集合查询失败: $e');
        }
      }

      // 按时间排序
      newReplies.sort((a, b) => b['created'].compareTo(a['created']));

      return newReplies;
    } catch (e) {
      debugPrint('获取新回复列表失败: $e');
      return [];
    }
  }

  /// 标记回复为已读
  Future<void> markReplyAsRead(String replyId, String type) async {
    try {
      final collection = type == 'spot_comment' ? 'comments' : 'post_comments';
      await _pb.collection(collection).update(replyId, body: {'is_read': true});
    } catch (e) {
      debugPrint('标记回复已读失败: $e');
    }
  }
}
