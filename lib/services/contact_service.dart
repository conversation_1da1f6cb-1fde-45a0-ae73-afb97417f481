import 'package:flutter/foundation.dart';
import '../config/pocketbase_config.dart';
import '../models/message/contact_category.dart';
import '../models/user.dart';
import 'auth_service_new.dart';

/// 联系人服务
///
/// 负责用户关系管理：关注、取消关注、拉黑、解除拉黑等
class ContactService {
  static final ContactService _instance = ContactService._internal();
  factory ContactService() => _instance;
  ContactService._internal();

  final AuthService _authService = AuthService();
  final _pb = PocketBaseConfig.instance.client;

  /// 获取当前用户
  User? get currentUser => _authService.currentUser;

  /// 获取联系人分类列表
  Future<List<ContactCategory>> getContactCategories() async {
    final user = currentUser;
    if (user == null) return [];

    try {
      // 并行获取各种关系的数量
      final results = await Future.wait([
        _getRelationCount(user.id, 'friend'),
        _getRelationCount(user.id, 'follow'),
        _getFollowerCount(user.id),
        _getRelationCount(user.id, 'block'),
      ]);

      return [
        ContactCategory(
          type: ContactCategoryType.friends,
          name: '好友',
          icon: '👥',
          count: results[0],
          showBadge: false,
        ),
        ContactCategory(
          type: ContactCategoryType.following,
          name: '关注',
          icon: '👁️',
          count: results[1],
          showBadge: false,
        ),
        ContactCategory(
          type: ContactCategoryType.followers,
          name: '粉丝',
          icon: '👤',
          count: results[2],
          showBadge: results[2] > 0,
          badgeCount: results[2],
        ),
        ContactCategory(
          type: ContactCategoryType.blocked,
          name: '黑名单',
          icon: '🚫',
          count: results[3],
          showBadge: false,
        ),
      ];
    } catch (e) {
      debugPrint('❌ [联系人服务] 获取分类失败: $e');
      return [];
    }
  }

  /// 获取指定分类的联系人列表
  Future<List<User>> getContactsByCategory(ContactCategoryType type) async {
    final user = currentUser;
    if (user == null) return [];

    try {
      switch (type) {
        case ContactCategoryType.friends:
          return await _getFriends(user.id);
        case ContactCategoryType.following:
          return await _getFollowing(user.id);
        case ContactCategoryType.followers:
          return await _getFollowers(user.id);
        case ContactCategoryType.blocked:
          return await _getBlocked(user.id);
      }
    } catch (e) {
      debugPrint('❌ [联系人服务] 获取联系人列表失败: $e');
      return [];
    }
  }

  /// 关注用户
  Future<bool> followUser(String targetUserId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 检查是否已经关注
      final existing = await _pb
          .collection('user_relations')
          .getList(
            filter:
                'follower = "${user.id}" && following = "$targetUserId" && relation_type = "follow"',
          );

      if (existing.items.isNotEmpty) {
        debugPrint('⚠️ [联系人服务] 已经关注该用户');
        return true;
      }

      // 创建关注关系
      await _pb
          .collection('user_relations')
          .create(
            body: {
              'follower': user.id,
              'following': targetUserId,
              'relation_type': 'follow',
            },
          );

      // 检查是否互相关注（成为好友）
      final mutualFollow = await _pb
          .collection('user_relations')
          .getList(
            filter:
                'follower = "$targetUserId" && following = "${user.id}" && relation_type = "follow"',
          );

      if (mutualFollow.items.isNotEmpty) {
        // 创建好友关系
        await _createFriendship(user.id, targetUserId);
      }

      debugPrint('✅ [联系人服务] 关注用户成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 关注用户失败: $e');
      return false;
    }
  }

  /// 取消关注用户
  Future<bool> unfollowUser(String targetUserId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 删除关注关系
      final followRecords = await _pb
          .collection('user_relations')
          .getList(
            filter:
                'follower = "${user.id}" && following = "$targetUserId" && relation_type = "follow"',
          );

      for (final record in followRecords.items) {
        await _pb.collection('user_relations').delete(record.id);
      }

      // 删除好友关系（如果存在）
      final friendRecords = await _pb
          .collection('user_relations')
          .getList(
            filter:
                '(follower = "${user.id}" && following = "$targetUserId" && relation_type = "friend") || (follower = "$targetUserId" && following = "${user.id}" && relation_type = "friend")',
          );

      for (final record in friendRecords.items) {
        await _pb.collection('user_relations').delete(record.id);
      }

      debugPrint('✅ [联系人服务] 取消关注成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 取消关注失败: $e');
      return false;
    }
  }

  /// 拉黑用户
  Future<bool> blockUser(String targetUserId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 先删除所有现有关系
      await unfollowUser(targetUserId);

      // 创建拉黑关系
      await _pb
          .collection('user_relations')
          .create(
            body: {
              'follower': user.id,
              'following': targetUserId,
              'relation_type': 'block',
            },
          );

      debugPrint('✅ [联系人服务] 拉黑用户成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 拉黑用户失败: $e');
      return false;
    }
  }

  /// 解除拉黑
  Future<bool> unblockUser(String targetUserId) async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // 删除拉黑关系
      final blockRecords = await _pb
          .collection('user_relations')
          .getList(
            filter:
                'follower = "${user.id}" && following = "$targetUserId" && relation_type = "block"',
          );

      for (final record in blockRecords.items) {
        await _pb.collection('user_relations').delete(record.id);
      }

      debugPrint('✅ [联系人服务] 解除拉黑成功: $targetUserId');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 解除拉黑失败: $e');
      return false;
    }
  }

  /// 接受陌生人对话
  Future<bool> acceptConversation(String conversationId) async {
    try {
      await _pb
          .collection('conversations')
          .update(conversationId, body: {'status': 'active'});

      debugPrint('✅ [联系人服务] 接受对话成功: $conversationId');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 接受对话失败: $e');
      return false;
    }
  }

  /// 拒绝陌生人对话并拉黑
  Future<bool> rejectAndBlockConversation(
    String conversationId,
    String otherUserId,
  ) async {
    try {
      // 拉黑用户
      await blockUser(otherUserId);

      // 删除对话
      await _pb.collection('conversations').delete(conversationId);

      debugPrint('✅ [联系人服务] 拒绝对话并拉黑成功');
      return true;
    } catch (e) {
      debugPrint('❌ [联系人服务] 拒绝对话失败: $e');
      return false;
    }
  }

  /// 获取关系数量
  Future<int> _getRelationCount(String userId, String relationType) async {
    try {
      final result = await _pb
          .collection('user_relations')
          .getList(
            filter: 'follower = "$userId" && relation_type = "$relationType"',
            perPage: 1,
          );
      return result.totalItems;
    } catch (e) {
      debugPrint('❌ [联系人服务] 获取关系数量失败: $e');
      return 0;
    }
  }

  /// 获取粉丝数量
  Future<int> _getFollowerCount(String userId) async {
    try {
      final result = await _pb
          .collection('user_relations')
          .getList(
            filter: 'following = "$userId" && relation_type = "follow"',
            perPage: 1,
          );
      return result.totalItems;
    } catch (e) {
      debugPrint('❌ [联系人服务] 获取粉丝数量失败: $e');
      return 0;
    }
  }

  /// 获取好友列表
  Future<List<User>> _getFriends(String userId) async {
    final records = await _pb
        .collection('user_relations')
        .getList(
          filter: 'follower = "$userId" && relation_type = "friend"',
          expand: 'following',
        );

    return _parseUserList(records.items, 'following');
  }

  /// 获取关注列表
  Future<List<User>> _getFollowing(String userId) async {
    final records = await _pb
        .collection('user_relations')
        .getList(
          filter: 'follower = "$userId" && relation_type = "follow"',
          expand: 'following',
        );

    return _parseUserList(records.items, 'following');
  }

  /// 获取粉丝列表
  Future<List<User>> _getFollowers(String userId) async {
    final records = await _pb
        .collection('user_relations')
        .getList(
          filter: 'following = "$userId" && relation_type = "follow"',
          expand: 'follower',
        );

    return _parseUserList(records.items, 'follower');
  }

  /// 获取黑名单列表
  Future<List<User>> _getBlocked(String userId) async {
    final records = await _pb
        .collection('user_relations')
        .getList(
          filter: 'follower = "$userId" && relation_type = "block"',
          expand: 'following',
        );

    return _parseUserList(records.items, 'following');
  }

  /// 创建好友关系
  Future<void> _createFriendship(String userId1, String userId2) async {
    try {
      // 为两个用户都创建好友关系
      await _pb
          .collection('user_relations')
          .create(
            body: {
              'follower': userId1,
              'following': userId2,
              'relation_type': 'friend',
            },
          );

      await _pb
          .collection('user_relations')
          .create(
            body: {
              'follower': userId2,
              'following': userId1,
              'relation_type': 'friend',
            },
          );

      debugPrint('✅ [联系人服务] 创建好友关系成功');
    } catch (e) {
      debugPrint('❌ [联系人服务] 创建好友关系失败: $e');
    }
  }

  /// 解析用户列表
  List<User> _parseUserList(List<dynamic> records, String expandField) {
    final users = <User>[];

    for (final record in records) {
      try {
        final userData = record.expand?[expandField];
        if (userData != null) {
          final user = _parseUser(userData);
          if (user != null) {
            users.add(user);
          }
        }
      } catch (e) {
        debugPrint('❌ [联系人服务] 解析用户失败: $e');
      }
    }

    return users;
  }

  /// 解析用户记录
  User? _parseUser(dynamic userData) {
    try {
      return User(
        id: userData['id'],
        username: userData['username'] ?? '',
        email: userData['email'] ?? '',
        avatar:
            userData['avatar'] != null
                ? _pb.files.getUrl(userData, userData['avatar']).toString()
                : null,
        phone: userData['phone'],
        created: DateTime.parse(userData['created']),
        updated: DateTime.parse(userData['updated']),
      );
    } catch (e) {
      debugPrint('❌ [联系人服务] 解析用户记录失败: $e');
      return null;
    }
  }
}
