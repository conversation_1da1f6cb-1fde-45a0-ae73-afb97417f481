import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/fishing_spot.dart';

/// 百度地图导航服务
///
/// 提供调用百度地图进行导航的功能
class BaiduMapNavigationService {
  static const String _appName = 'andr.fishing.app';

  /// 导航到钓点
  ///
  /// [spot] 钓点信息
  /// [context] 上下文，用于显示错误提示
  static Future<bool> navigateToSpot(
    FishingSpot spot,
    BuildContext context,
  ) async {
    try {
      debugPrint('🗺️ [导航] 开始检查钓点坐标信息');
      debugPrint('🗺️ [导航] 钓点名称: ${spot.name}');
      debugPrint('🗺️ [导航] 钓点location数据: ${spot.location}');

      // 检查钓点是否有坐标信息
      if (spot.location == null) {
        debugPrint('❌ [导航] 钓点location为null');
        if (context.mounted) {
          _showErrorSnackBar(context, '钓点坐标信息不完整，无法导航');
        }
        return false;
      }

      // 检查坐标字段（支持lon和lng两种格式）
      final lat = spot.location!['lat'];
      final lng = spot.location!['lng'] ?? spot.location!['lon'];

      debugPrint('🗺️ [导航] 解析坐标: lat=$lat, lng=$lng');

      if (lat == null || lng == null) {
        debugPrint('❌ [导航] 坐标字段缺失: lat=$lat, lng=$lng');
        if (context.mounted) {
          _showErrorSnackBar(context, '钓点坐标信息不完整，无法导航');
        }
        return false;
      }

      final spotName = spot.name.isNotEmpty ? spot.name : '钓点';

      // 构建百度地图导航URL
      final url = _buildNavigationUrl(
        latitude: lat.toDouble(),
        longitude: lng.toDouble(),
        destinationName: spotName,
      );

      debugPrint('🗺️ [导航] 准备调用百度地图导航: $url');

      // 尝试启动百度地图
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('✅ [导航] 成功调用百度地图导航');
        return true;
      } else {
        debugPrint('❌ [导航] 无法启动百度地图，可能未安装');
        if (context.mounted) {
          _showErrorSnackBar(context, '未安装百度地图或无法启动导航');
        }
        return false;
      }
    } catch (e) {
      debugPrint('❌ [导航] 导航失败: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, '导航失败，请重试');
      }
      return false;
    }
  }

  /// 导航到指定坐标
  ///
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [destinationName] 目的地名称
  /// [context] 上下文，用于显示错误提示
  static Future<bool> navigateToCoordinate({
    required double latitude,
    required double longitude,
    required String destinationName,
    required BuildContext context,
  }) async {
    try {
      final url = _buildNavigationUrl(
        latitude: latitude,
        longitude: longitude,
        destinationName: destinationName,
      );

      debugPrint('🗺️ [导航] 准备导航到坐标: $latitude, $longitude');

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('✅ [导航] 成功启动导航');
        return true;
      } else {
        debugPrint('❌ [导航] 无法启动百度地图');
        if (context.mounted) {
          _showErrorSnackBar(context, '未安装百度地图或无法启动导航');
        }
        return false;
      }
    } catch (e) {
      debugPrint('❌ [导航] 导航失败: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, '导航失败，请重试');
      }
      return false;
    }
  }

  /// 构建百度地图导航URL
  ///
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [destinationName] 目的地名称
  /// [coordinateType] 坐标类型，默认为bd09ll（百度经纬度坐标）
  static String _buildNavigationUrl({
    required double latitude,
    required double longitude,
    required String destinationName,
    String coordinateType = 'bd09ll',
  }) {
    // 对目的地名称进行URL编码
    final encodedName = Uri.encodeComponent(destinationName);

    // 构建百度地图导航URL
    // 使用驾车导航模式
    return 'baidumap://map/navi?'
        'query=$encodedName&'
        'location=$latitude,$longitude&'
        'coord_type=$coordinateType&'
        'src=$_appName';
  }

  /// 检查是否安装了百度地图
  static Future<bool> isBaiduMapInstalled() async {
    try {
      final uri = Uri.parse('baidumap://map?src=$_appName');
      return await canLaunchUrl(uri);
    } catch (e) {
      debugPrint('❌ [导航] 检查百度地图安装状态失败: $e');
      return false;
    }
  }

  /// 显示错误提示
  static void _showErrorSnackBar(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// 显示导航选项对话框
  ///
  /// 当用户点击导航时，可以选择不同的导航方式
  static Future<void> showNavigationOptions({
    required BuildContext context,
    required FishingSpot spot,
  }) async {
    // 检查是否安装了百度地图
    final isBaiduInstalled = await isBaiduMapInstalled();

    if (!context.mounted) return;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('选择导航方式', style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: 16),

              // 百度地图导航
              if (isBaiduInstalled)
                ListTile(
                  leading: const Icon(Icons.navigation, color: Colors.blue),
                  title: const Text('百度地图导航'),
                  subtitle: const Text('使用百度地图进行导航'),
                  onTap: () {
                    Navigator.pop(context);
                    navigateToSpot(spot, context);
                  },
                ),

              // 如果没有安装百度地图，显示提示
              if (!isBaiduInstalled)
                const ListTile(
                  leading: Icon(Icons.warning, color: Colors.orange),
                  title: Text('未安装百度地图'),
                  subtitle: Text('请先安装百度地图应用'),
                ),

              const SizedBox(height: 8),

              // 取消按钮
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
            ],
          ),
        );
      },
    );
  }
}
