import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'dart:io';

/// 自定义图片缓存管理器
///
/// 功能：
/// 1. 3个月缓存过期时间
/// 2. 合理的缓存大小限制
/// 3. 支持不同类型图片的缓存策略
/// 4. 使用持久化存储目录，避免应用重启后缓存丢失
class ImageCacheManager {
  static const String _cacheKey = 'fishing_app_images';

  // 缓存配置
  static const Duration _cacheExpiry = Duration(days: 90); // 3个月
  static const int _maxCacheObjects = 1000; // 最大缓存对象数
  static const int _maxCacheSize = 200 * 1024 * 1024; // 200MB最大缓存大小

  // 延迟初始化的缓存管理器
  static CacheManager? _spotPhotos;
  static CacheManager? _avatars;
  static bool _initialized = false;

  /// 初始化缓存管理器
  /// 创建持久化缓存目录
  static Future<void> initialize() async {
    if (_initialized) {
      debugPrint('🔄 [图片缓存] 缓存管理器已初始化，跳过');
      return;
    }

    debugPrint('🚀 [图片缓存] 开始初始化持久化缓存管理器...');

    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      debugPrint('📁 [图片缓存] 应用文档目录: ${appDocDir.path}');

      final spotPhotosDir = Directory(
        path.join(appDocDir.path, 'image_cache', 'spot_photos'),
      );
      final avatarsDir = Directory(
        path.join(appDocDir.path, 'image_cache', 'avatars'),
      );

      debugPrint('📁 [图片缓存] 钓点照片缓存目录: ${spotPhotosDir.path}');
      debugPrint('📁 [图片缓存] 头像缓存目录: ${avatarsDir.path}');

      // 确保目录存在
      if (!await spotPhotosDir.exists()) {
        await spotPhotosDir.create(recursive: true);
        debugPrint('🗂️ [图片缓存] 创建钓点照片缓存目录: ${spotPhotosDir.path}');
      } else {
        debugPrint('✅ [图片缓存] 钓点照片缓存目录已存在: ${spotPhotosDir.path}');
      }

      if (!await avatarsDir.exists()) {
        await avatarsDir.create(recursive: true);
        debugPrint('🗂️ [图片缓存] 创建头像缓存目录: ${avatarsDir.path}');
      } else {
        debugPrint('✅ [图片缓存] 头像缓存目录已存在: ${avatarsDir.path}');
      }

      // 创建缓存管理器
      debugPrint('🔧 [图片缓存] 创建钓点照片缓存管理器...');
      _spotPhotos = CacheManager(
        Config(
          _cacheKey,
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: _maxCacheObjects,
          repo: JsonCacheInfoRepository(databaseName: _cacheKey),
          fileService: HttpFileService(),
          fileSystem: IOFileSystem(spotPhotosDir.path),
        ),
      );
      debugPrint('✅ [图片缓存] 钓点照片缓存管理器创建完成');

      debugPrint('🔧 [图片缓存] 创建头像缓存管理器...');
      _avatars = CacheManager(
        Config(
          '${_cacheKey}_avatars',
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: 500, // 头像数量相对较少
          repo: JsonCacheInfoRepository(databaseName: '${_cacheKey}_avatars'),
          fileService: HttpFileService(),
          fileSystem: IOFileSystem(avatarsDir.path),
        ),
      );
      debugPrint('✅ [图片缓存] 头像缓存管理器创建完成');

      _initialized = true;
      debugPrint('🎉 [图片缓存] 持久化缓存管理器初始化完成！');

      // 验证缓存管理器是否正常工作
      await _verifyCacheManagers(spotPhotosDir, avatarsDir);
    } catch (e) {
      debugPrint('❌ [图片缓存] 初始化失败: $e');
      // 回退到默认配置
      _spotPhotos = CacheManager(
        Config(
          _cacheKey,
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: _maxCacheObjects,
          repo: JsonCacheInfoRepository(databaseName: _cacheKey),
          fileService: HttpFileService(),
        ),
      );

      _avatars = CacheManager(
        Config(
          '${_cacheKey}_avatars',
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: 500,
          repo: JsonCacheInfoRepository(databaseName: '${_cacheKey}_avatars'),
          fileService: HttpFileService(),
        ),
      );
      _initialized = true;
    }
  }

  /// 获取钓点照片缓存管理器
  static CacheManager get spotPhotos {
    if (!_initialized) {
      // 如果未初始化，返回默认配置的缓存管理器
      debugPrint('⚠️ [图片缓存] 缓存管理器未初始化，使用默认配置');
      return CacheManager(
        Config(
          _cacheKey,
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: _maxCacheObjects,
          repo: JsonCacheInfoRepository(databaseName: _cacheKey),
          fileService: HttpFileService(),
        ),
      );
    }
    return _spotPhotos!;
  }

  /// 获取头像缓存管理器
  static CacheManager get avatars {
    if (!_initialized) {
      // 如果未初始化，返回默认配置的缓存管理器
      debugPrint('⚠️ [图片缓存] 缓存管理器未初始化，使用默认配置');
      return CacheManager(
        Config(
          '${_cacheKey}_avatars',
          stalePeriod: _cacheExpiry,
          maxNrOfCacheObjects: 500,
          repo: JsonCacheInfoRepository(databaseName: '${_cacheKey}_avatars'),
          fileService: HttpFileService(),
        ),
      );
    }
    return _avatars!;
  }

  /// 获取缓存统计信息
  static Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final spotPhotosFiles =
          await spotPhotos.getFileFromCache('dummy') != null
              ? await _getCacheFileCount(spotPhotos)
              : 0;
      final avatarsFiles =
          await avatars.getFileFromCache('dummy') != null
              ? await _getCacheFileCount(avatars)
              : 0;

      return {
        'spotPhotos': {'fileCount': spotPhotosFiles, 'cacheKey': _cacheKey},
        'avatars': {
          'fileCount': avatarsFiles,
          'cacheKey': '${_cacheKey}_avatars',
        },
        'totalFiles': spotPhotosFiles + avatarsFiles,
        'cacheExpiry': '${_cacheExpiry.inDays}天',
        'maxCacheObjects': _maxCacheObjects,
        'maxCacheSize':
            '${(_maxCacheSize / (1024 * 1024)).toStringAsFixed(0)}MB',
      };
    } catch (e) {
      debugPrint('❌ [图片缓存] 获取缓存统计失败: $e');
      return {
        'error': e.toString(),
        'spotPhotos': {'fileCount': 0},
        'avatars': {'fileCount': 0},
        'totalFiles': 0,
      };
    }
  }

  /// 获取缓存文件数量（辅助方法）
  static Future<int> _getCacheFileCount(CacheManager manager) async {
    try {
      // 这是一个简化的实现，实际的文件计数可能需要更复杂的逻辑
      return 0; // 暂时返回0，可以后续优化
    } catch (e) {
      debugPrint('❌ [图片缓存] 获取文件数量失败: $e');
      return 0;
    }
  }

  /// 清除所有缓存
  static Future<void> clearAllCache() async {
    try {
      debugPrint('🧹 [图片缓存] 开始清除所有缓存');

      await Future.wait([spotPhotos.emptyCache(), avatars.emptyCache()]);

      debugPrint('✅ [图片缓存] 所有缓存清除完成');
    } catch (e) {
      debugPrint('❌ [图片缓存] 清除缓存失败: $e');
      rethrow;
    }
  }

  /// 清除钓点照片缓存
  static Future<void> clearSpotPhotosCache() async {
    try {
      debugPrint('🧹 [图片缓存] 清除钓点照片缓存');
      await spotPhotos.emptyCache();
      debugPrint('✅ [图片缓存] 钓点照片缓存清除完成');
    } catch (e) {
      debugPrint('❌ [图片缓存] 清除钓点照片缓存失败: $e');
      rethrow;
    }
  }

  /// 清除头像缓存
  static Future<void> clearAvatarsCache() async {
    try {
      debugPrint('🧹 [图片缓存] 清除头像缓存');
      await avatars.emptyCache();
      debugPrint('✅ [图片缓存] 头像缓存清除完成');
    } catch (e) {
      debugPrint('❌ [图片缓存] 清除头像缓存失败: $e');
      rethrow;
    }
  }

  /// 清除过期缓存
  static Future<void> clearExpiredCache() async {
    try {
      debugPrint('🧹 [图片缓存] 清除过期缓存');

      // CacheManager会自动处理过期文件，这里主要是触发清理
      await Future.wait([
        spotPhotos.removeFile('dummy_trigger_cleanup'),
        avatars.removeFile('dummy_trigger_cleanup'),
      ]);

      debugPrint('✅ [图片缓存] 过期缓存清理完成');
    } catch (e) {
      debugPrint('❌ [图片缓存] 清除过期缓存失败: $e');
      // 不抛出异常，因为这是清理操作
    }
  }

  /// 预加载图片到缓存
  static Future<void> preloadImage(String url, {bool isAvatar = false}) async {
    try {
      final manager = isAvatar ? avatars : spotPhotos;
      await manager.downloadFile(url);
      debugPrint('✅ [图片缓存] 预加载完成: $url');
    } catch (e) {
      debugPrint('❌ [图片缓存] 预加载失败: $url - $e');
    }
  }

  /// 检查图片是否已缓存
  static Future<bool> isImageCached(String url, {bool isAvatar = false}) async {
    try {
      final manager = isAvatar ? avatars : spotPhotos;
      final file = await manager.getFileFromCache(url);

      if (file != null) {
        debugPrint('✅ [图片缓存] 缓存命中: ${url.substring(0, 50)}...');
        // 额外验证：检查文件是否真的存在
        final fileExists = await file.file.exists();
        debugPrint('📁 [图片缓存] 缓存文件存在: $fileExists');
        return fileExists;
      } else {
        debugPrint('❌ [图片缓存] 缓存未命中: ${url.substring(0, 50)}...');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [图片缓存] 检查缓存状态失败: $url - $e');
      return false;
    }
  }

  /// 获取缓存文件信息（用于调试）
  static Future<Map<String, dynamic>> getCacheFileInfo(
    String url, {
    bool isAvatar = false,
  }) async {
    try {
      final manager = isAvatar ? avatars : spotPhotos;
      final fileInfo = await manager.getFileFromCache(url);

      if (fileInfo != null) {
        return {
          'cached': true,
          'url': fileInfo.originalUrl,
          'validTill': fileInfo.validTill.toIso8601String(),
          'source': fileInfo.source.toString(),
        };
      } else {
        return {'cached': false};
      }
    } catch (e) {
      debugPrint('❌ [图片缓存] 获取缓存文件信息失败: $url - $e');
      return {'cached': false, 'error': e.toString()};
    }
  }

  /// 验证缓存管理器是否正常工作
  static Future<void> _verifyCacheManagers(
    Directory spotPhotosDir,
    Directory avatarsDir,
  ) async {
    try {
      debugPrint('🔍 [图片缓存] 开始验证缓存管理器...');

      // 检查目录是否可写
      final testFile1 = File(path.join(spotPhotosDir.path, 'test_write.tmp'));
      final testFile2 = File(path.join(avatarsDir.path, 'test_write.tmp'));

      await testFile1.writeAsString('test');
      await testFile2.writeAsString('test');

      if (await testFile1.exists() && await testFile2.exists()) {
        debugPrint('✅ [图片缓存] 缓存目录可写入');
        await testFile1.delete();
        await testFile2.delete();
      }

      // 检查缓存管理器是否可用
      if (_spotPhotos != null && _avatars != null) {
        debugPrint('✅ [图片缓存] 缓存管理器实例创建成功');
      } else {
        debugPrint('❌ [图片缓存] 缓存管理器实例创建失败');
      }

      debugPrint('🎯 [图片缓存] 缓存管理器验证完成');
    } catch (e) {
      debugPrint('❌ [图片缓存] 缓存管理器验证失败: $e');
    }
  }

  /// 调试方法：打印缓存状态
  static Future<void> debugCacheStatus() async {
    try {
      debugPrint('🔍 [图片缓存] === 缓存状态调试 ===');
      debugPrint('🔍 [图片缓存] 初始化状态: $_initialized');
      debugPrint('🔍 [图片缓存] 钓点照片管理器: ${_spotPhotos != null ? "已创建" : "未创建"}');
      debugPrint('🔍 [图片缓存] 头像管理器: ${_avatars != null ? "已创建" : "未创建"}');

      final stats = await getCacheStats();
      debugPrint('🔍 [图片缓存] 缓存统计: $stats');

      debugPrint('🔍 [图片缓存] === 调试完成 ===');
    } catch (e) {
      debugPrint('❌ [图片缓存] 调试失败: $e');
    }
  }
}
