/// 应用主题类型枚举
/// 
/// 定义了应用支持的主题类型，基于Material Design 3规范
enum AppThemeType {
  /// 经典浅色主题 - 适合白天使用，基于Material Blue 500
  light('light', '浅色主题', 'Light Theme'),
  
  /// 经典深色主题 - 适合夜间使用，基于Material Purple 200
  dark('dark', '深色主题', 'Dark Theme');

  const AppThemeType(this.key, this.displayName, this.englishName);

  /// 主题键值，用于存储和识别
  final String key;
  
  /// 中文显示名称
  final String displayName;
  
  /// 英文名称
  final String englishName;

  /// 从字符串键值获取主题类型
  static AppThemeType fromKey(String key) {
    for (final theme in AppThemeType.values) {
      if (theme.key == key) {
        return theme;
      }
    }
    return AppThemeType.light; // 默认主题
  }

  /// 获取主题描述
  String get description {
    switch (this) {
      case AppThemeType.light:
        return '经典浅色主题，基于Material Design 3规范，适合白天使用，提供清晰的视觉体验';
      case AppThemeType.dark:
        return '经典深色主题，基于Material Design 3规范，适合夜间使用，减少眼部疲劳';
    }
  }

  /// 获取主题图标
  String get iconEmoji {
    switch (this) {
      case AppThemeType.light:
        return '☀️';
      case AppThemeType.dark:
        return '🌙';
    }
  }
}