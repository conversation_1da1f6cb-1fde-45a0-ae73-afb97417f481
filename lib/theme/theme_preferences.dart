import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'app_theme_type.dart';

/// 主题偏好设置管理
/// 
/// 负责主题相关设置的持久化存储和读取
class ThemePreferences {
  static const String _themeTypeKey = 'app_theme_type';
  static const String _themeModeKey = 'app_theme_mode';
  static const String _useSystemDynamicColorKey = 'use_system_dynamic_color';

  /// 保存主题类型
  static Future<void> saveThemeType(AppThemeType themeType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeTypeKey, themeType.key);
    } catch (e) {
      debugPrint('保存主题类型失败: $e');
    }
  }

  /// 获取主题类型
  static Future<AppThemeType> getThemeType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeKey = prefs.getString(_themeTypeKey);
      if (themeKey != null) {
        return AppThemeType.fromKey(themeKey);
      }
    } catch (e) {
      debugPrint('获取主题类型失败: $e');
    }
    return AppThemeType.light; // 默认主题
  }

  /// 保存主题模式
  static Future<void> saveThemeMode(ThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeModeKey, themeMode.name);
    } catch (e) {
      debugPrint('保存主题模式失败: $e');
    }
  }

  /// 获取主题模式
  static Future<ThemeMode> getThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(_themeModeKey);
      if (themeModeString != null) {
        return ThemeMode.values.firstWhere(
          (mode) => mode.name == themeModeString,
          orElse: () => ThemeMode.system,
        );
      }
    } catch (e) {
      debugPrint('获取主题模式失败: $e');
    }
    return ThemeMode.system; // 默认跟随系统
  }

  /// 保存是否使用系统动态颜色
  static Future<void> saveUseSystemDynamicColor(bool useSystemDynamicColor) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_useSystemDynamicColorKey, useSystemDynamicColor);
    } catch (e) {
      debugPrint('保存动态颜色设置失败: $e');
    }
  }

  /// 获取是否使用系统动态颜色
  static Future<bool> getUseSystemDynamicColor() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_useSystemDynamicColorKey) ?? false;
    } catch (e) {
      debugPrint('获取动态颜色设置失败: $e');
      return false;
    }
  }

  /// 清除所有主题设置
  static Future<void> clearAllThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_themeTypeKey);
      await prefs.remove(_themeModeKey);
      await prefs.remove(_useSystemDynamicColorKey);
    } catch (e) {
      debugPrint('清除主题设置失败: $e');
    }
  }
}