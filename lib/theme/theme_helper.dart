import 'package:flutter/material.dart';

/// 主题辅助工具类
/// 
/// 提供便捷的主题颜色和样式访问方法，简化主题使用
class ThemeHelper {
  ThemeHelper._();

  /// 获取当前主题的颜色方案
  static ColorScheme colorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }

  /// 获取当前主题的文字主题
  static TextTheme textTheme(BuildContext context) {
    return Theme.of(context).textTheme;
  }

  /// 获取当前主题的亮度
  static Brightness brightness(BuildContext context) {
    return Theme.of(context).brightness;
  }

  /// 判断是否为深色主题
  static bool isDarkMode(BuildContext context) {
    return brightness(context) == Brightness.dark;
  }

  /// 获取适合当前主题的表面颜色
  /// 用于卡片、对话框等表面背景
  static Color surfaceColor(BuildContext context) {
    return colorScheme(context).surface;
  }

  /// 获取适合当前主题的主要文字颜色
  static Color primaryTextColor(BuildContext context) {
    return colorScheme(context).onSurface;
  }

  /// 获取适合当前主题的次要文字颜色
  /// 用于提示文字、说明文字等
  static Color secondaryTextColor(BuildContext context) {
    return colorScheme(context).onSurfaceVariant;
  }

  /// 获取适合当前主题的输入框背景颜色
  static Color inputBackgroundColor(BuildContext context) {
    return colorScheme(context).surfaceContainerHighest;
  }

  /// 获取适合当前主题的边框颜色
  static Color borderColor(BuildContext context) {
    return colorScheme(context).outline;
  }

  /// 获取适合当前主题的阴影颜色
  static Color shadowColor(BuildContext context) {
    return colorScheme(context).shadow;
  }

  /// 获取主要强调色
  static Color primaryColor(BuildContext context) {
    return colorScheme(context).primary;
  }

  /// 获取错误状态颜色
  static Color errorColor(BuildContext context) {
    return colorScheme(context).error;
  }

  /// 获取成功状态颜色（使用次要色）
  static Color successColor(BuildContext context) {
    return colorScheme(context).secondary;
  }

  /// 获取警告状态颜色（使用第三色）
  static Color warningColor(BuildContext context) {
    return colorScheme(context).tertiary;
  }

  /// 创建适合当前主题的文字样式
  static TextStyle createTextStyle(
    BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? primaryTextColor(context),
      height: height,
    );
  }

  /// 创建适合当前主题的次要文字样式
  static TextStyle createSecondaryTextStyle(
    BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    return createTextStyle(
      context,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: secondaryTextColor(context),
    );
  }

  /// 创建适合当前主题的装饰容器
  static BoxDecoration createSurfaceDecoration(
    BuildContext context, {
    double borderRadius = 8.0,
    bool withBorder = false,
    bool withShadow = false,
  }) {
    return BoxDecoration(
      color: surfaceColor(context),
      borderRadius: BorderRadius.circular(borderRadius),
      border: withBorder 
          ? Border.all(color: borderColor(context))
          : null,
      boxShadow: withShadow
          ? [
              BoxShadow(
                color: shadowColor(context).withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ]
          : null,
    );
  }

  /// 创建适合当前主题的输入框装饰
  static InputDecoration createInputDecoration(
    BuildContext context, {
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    double borderRadius = 8.0,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: createSecondaryTextStyle(context),
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: inputBackgroundColor(context),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(color: borderColor(context)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(color: borderColor(context)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(color: primaryColor(context), width: 2),
      ),
    );
  }
}