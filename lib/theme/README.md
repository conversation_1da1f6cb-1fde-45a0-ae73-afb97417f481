# 🎨 主题系统架构文档

## 概述

本项目实现了一套完整的Material 3主题系统，支持多种预设主题和实时主题切换。

## 🏗️ 架构组件

### 1. 核心文件

- `app_theme_type.dart` - 主题类型枚举定义
- `app_theme_manager.dart` - 主题管理器（状态管理）
- `material3_theme_data.dart` - Material 3主题数据生成器
- `theme_preferences.dart` - 主题偏好设置持久化
- `theme.dart` - 统一导出文件

### 2. UI组件

- `theme_selector.dart` - 主题选择器页面
- `theme_preview_demo.dart` - 主题预览演示页面

## 🎯 支持的主题

### 1. 海洋蓝调 (Ocean Blue)
- **种子颜色**: `#0077BE`
- **设计理念**: 深海蓝调，营造专业可靠的钓鱼氛围
- **适用场景**: 商务用户、专业钓鱼爱好者

### 2. 自然绿意 (Nature Green)
- **种子颜色**: `#2E7D32`
- **设计理念**: 贴近自然环境，体现户外钓鱼的生态感
- **适用场景**: 环保意识用户、自然爱好者

### 3. 日落暖调 (Sunset Warm)
- **种子颜色**: `#FF6B35`
- **设计理念**: 黄昏钓鱼的温暖时光，活力而温馨
- **适用场景**: 年轻用户、社交活跃用户

### 4. 夜钓深邃 (Night Fishing)
- **种子颜色**: `#37474F`
- **设计理念**: 夜钓的神秘感，现代简约风格
- **适用场景**: 夜间使用、极简主义用户

### 5. 经典商务 (Classic Business)
- **种子颜色**: `#1565C0`
- **设计理念**: 经典商务蓝，专业可靠
- **适用场景**: 企业用户、正式场合

## 🔧 使用方法

### 1. 基本使用

```dart
import 'package:fishing_app/theme/theme.dart';

// 获取主题管理器实例
final themeManager = AppThemeManager.instance;

// 切换主题
await themeManager.setThemeType(AppThemeType.oceanBlue);

// 切换主题模式
await themeManager.setThemeMode(ThemeMode.dark);

// 获取当前主题数据
final lightTheme = themeManager.getLightThemeData();
final darkTheme = themeManager.getDarkThemeData();
```

### 2. 在Widget中监听主题变化

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: AppThemeManager.instance,
      builder: (context, child) {
        // 主题变化时会自动重建
        return Container(
          color: Theme.of(context).colorScheme.primary,
          child: Text('当前主题: ${AppThemeManager.instance.currentThemeType.displayName}'),
        );
      },
    );
  }
}
```

### 3. 应用级别集成

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: AppThemeManager.instance,
      builder: (context, child) {
        final themeManager = AppThemeManager.instance;
        
        return MaterialApp(
          theme: themeManager.getLightThemeData(),
          darkTheme: themeManager.getDarkThemeData(),
          themeMode: themeManager.currentThemeMode,
          // ... 其他配置
        );
      },
    );
  }
}
```

## 🎨 Material 3 特性

### 1. 完整的ColorScheme支持
- Primary, Secondary, Tertiary颜色系统
- Surface, Background颜色系统
- Error, Outline颜色系统
- 自动生成的对比色和容器色

### 2. 组件主题定制
- AppBar, Card, Button主题
- Input, Navigation主题
- Dialog, BottomSheet主题
- Switch, Checkbox, Radio主题

### 3. 自动适配
- 浅色/深色模式自动适配
- 可访问性对比度保证
- 动态颜色支持（可选）

## 📱 用户界面

### 1. 主题选择器
- 网格布局的主题卡片
- 实时预览效果
- 主题模式切换
- 动态颜色开关

### 2. 主题预览
- 各种组件的效果展示
- 颜色系统可视化
- 实时主题切换测试

## 🔄 数据持久化

主题设置会自动保存到本地存储：
- 主题类型选择
- 主题模式设置
- 动态颜色偏好

## 🚀 性能优化

1. **延迟初始化**: 主题管理器采用单例模式，按需初始化
2. **批量更新**: 主题变化时统一通知所有监听者
3. **缓存机制**: ThemeData对象会被缓存，避免重复生成
4. **异步操作**: 所有持久化操作都是异步的，不阻塞UI

## 🔧 扩展指南

### 1. 添加新主题

```dart
// 1. 在AppThemeType中添加新枚举值
enum AppThemeType {
  // ... 现有主题
  newTheme('newTheme', '新主题', 'New Theme'),
}

// 2. 在Material3ThemeData中添加种子颜色
static const Color _newThemeSeed = Color(0xFF123456);

// 3. 在getSeedColor方法中添加case
case AppThemeType.newTheme:
  return _newThemeSeed;
```

### 2. 自定义组件主题

```dart
// 在Material3ThemeData的ThemeData生成方法中添加
customComponentTheme: CustomComponentThemeData(
  // 自定义主题配置
),
```

## 🐛 故障排除

### 1. 主题不生效
- 检查是否正确初始化了AppThemeManager
- 确认使用了ListenableBuilder监听主题变化
- 验证MaterialApp使用了正确的theme和darkTheme

### 2. 持久化失败
- 检查SharedPreferences权限
- 查看控制台错误日志
- 确认异步操作正确处理

### 3. 性能问题
- 避免在build方法中直接调用主题管理器方法
- 使用ListenableBuilder而不是setState
- 检查是否有不必要的重建

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持5套预设主题
- Material 3完整支持
- 主题选择器UI
- 数据持久化

## 🤝 贡献指南

1. 新主题设计需要考虑可访问性
2. 遵循Material 3设计规范
3. 保持向后兼容性
4. 添加相应的测试用例
5. 更新文档说明