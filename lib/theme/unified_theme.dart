import 'package:flutter/material.dart';

/// 应用统一主题管理
///
/// 集中管理应用的所有主题配置，包括：
/// - Material 3 主题配置
/// - ConvexAppBar 样式配置
/// - 颜色系统
/// - 字体系统
/// - 组件样式
///
/// 迁移说明：
/// - 替代 lib/theme.dart 中的旧主题配置
/// - 整合 lib/theme/app_design_system.dart 的设计系统
/// - 统一管理所有主题相关配置
class AppTheme {
  AppTheme._();

  // ========== 颜色配置 ==========

  /// 应用主色调
  static const Color primarySeedColor = Color.fromARGB(255, 0, 136, 255);

  /// 登录页面渐变色（保持兼容性）
  static const Color loginGradientStart = Color(0xFFfbab66);
  static const Color loginGradientEnd = Color(0xFFf7418c);

  /// 旧主题兼容色彩（来自CustomTheme）
  static const Color legacyPrimaryColor = Color(0xFF3F51B5);
  static const Color legacyPrimaryLightColor = Color(0xFF7986CB);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  /// 设计系统色彩（来自AppDesignSystem）
  static const Color designPrimaryColor = Color(0xFF2196F3);
  static const Color designPrimaryLightColor = Color(0xFF64B5F6);
  static const Color designPrimaryDarkColor = Color(0xFF1976D2);

  /// 辅助色调
  static const Color secondaryColor = Color(0xFF4CAF50);
  static const Color secondaryLightColor = Color(0xFF81C784);
  static const Color secondaryDarkColor = Color(0xFF388E3C);

  /// 强调色
  static const Color accentColor = Color(0xFFFF9800);
  static const Color accentLightColor = Color(0xFFFFB74D);
  static const Color accentDarkColor = Color(0xFFF57C00);

  /// 状态颜色
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);

  /// 中性色
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Colors.white;
  static const Color cardColor = Colors.white;

  /// 文字颜色
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textHintColor = Color(0xFFBDBDBD);

  /// 边框颜色
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color dividerColor = Color(0xFFEEEEEE);

  // ========== 字体系统 ==========

  /// 标题字体样式
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
    height: 1.2,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
    height: 1.3,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
    height: 1.3,
  );

  /// 正文字体样式
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: textPrimaryColor,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textPrimaryColor,
    height: 1.4,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: textSecondaryColor,
    height: 1.4,
  );

  /// 标签字体样式
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textSecondaryColor,
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: textHintColor,
  );

  // ========== 间距系统 ==========

  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  // ========== 圆角系统 ==========

  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;

  // ========== 阴影系统 ==========

  static const BoxShadow shadowLight = BoxShadow(
    color: Color(0x0F000000),
    offset: Offset(0, 1),
    blurRadius: 3,
    spreadRadius: 0,
  );

  static const BoxShadow shadowMedium = BoxShadow(
    color: Color(0x1A000000),
    offset: Offset(0, 2),
    blurRadius: 6,
    spreadRadius: 0,
  );

  static const BoxShadow shadowHeavy = BoxShadow(
    color: Color(0x26000000),
    offset: Offset(0, 4),
    blurRadius: 12,
    spreadRadius: 0,
  );

  // ========== Material 3 主题 ==========

  /// 浅色主题
  static ThemeData get lightTheme => ThemeData(
    colorScheme: ColorScheme.fromSeed(
      seedColor: primarySeedColor,
      brightness: Brightness.light,
    ),
    useMaterial3: true,
    appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
  );

  /// 深色主题
  static ThemeData get darkTheme => ThemeData(
    colorScheme: ColorScheme.fromSeed(
      seedColor: primarySeedColor,
      brightness: Brightness.dark,
    ),
    useMaterial3: true,
    appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
  );

  // ========== 渐变配置（登录页面兼容性） ==========

  /// 主渐变
  static const LinearGradient primaryGradient = LinearGradient(
    colors: <Color>[loginGradientStart, loginGradientEnd],
    stops: <double>[0.0, 1.0],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  /// 按钮渐变
  static const LinearGradient buttonGradient = LinearGradient(
    colors: <Color>[loginGradientEnd, loginGradientStart],
    begin: FractionalOffset(0.2, 0.2),
    end: FractionalOffset(1.0, 1.0),
    stops: <double>[0.0, 1.0],
    tileMode: TileMode.clamp,
  );

  /// 按钮阴影
  static List<BoxShadow> get buttonShadow => [
    const BoxShadow(
      color: loginGradientStart,
      offset: Offset(1.0, 6.0),
      blurRadius: 20.0,
    ),
    const BoxShadow(
      color: loginGradientEnd,
      offset: Offset(1.0, 6.0),
      blurRadius: 20.0,
    ),
  ];

  // ========== 组件样式 ==========

  /// 卡片装饰
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: cardColor,
    borderRadius: BorderRadius.circular(radiusM),
    boxShadow: const [shadowLight],
  );

  /// 输入框装饰
  static InputDecoration inputDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) => InputDecoration(
    labelText: labelText,
    hintText: hintText,
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    enabled: enabled,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusS),
      borderSide: const BorderSide(color: borderColor),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusS),
      borderSide: const BorderSide(color: borderColor),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusS),
      borderSide: const BorderSide(color: designPrimaryColor, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusS),
      borderSide: const BorderSide(color: errorColor),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusS),
      borderSide: BorderSide(color: borderColor.withValues(alpha: 0.5)),
    ),
    filled: true,
    fillColor: enabled ? surfaceColor : backgroundColor,
    contentPadding: const EdgeInsets.symmetric(
      horizontal: spacingM,
      vertical: spacingM,
    ),
  );

  /// 按钮样式
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: designPrimaryColor,
    foregroundColor: Colors.white,
    elevation: 2,
    shadowColor: designPrimaryColor.withValues(alpha: 0.3),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radiusS)),
    padding: const EdgeInsets.symmetric(
      horizontal: spacingL,
      vertical: spacingM,
    ),
    textStyle: labelLarge.copyWith(color: Colors.white),
  );

  static ButtonStyle get secondaryButtonStyle => OutlinedButton.styleFrom(
    foregroundColor: designPrimaryColor,
    side: const BorderSide(color: designPrimaryColor),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radiusS)),
    padding: const EdgeInsets.symmetric(
      horizontal: spacingL,
      vertical: spacingM,
    ),
    textStyle: labelLarge.copyWith(color: designPrimaryColor),
  );

  /// 选择器样式
  static BoxDecoration selectorDecoration({
    required bool isSelected,
    Color? selectedColor,
  }) => BoxDecoration(
    color:
        isSelected
            ? (selectedColor ?? designPrimaryColor).withValues(alpha: 0.1)
            : backgroundColor,
    borderRadius: BorderRadius.circular(radiusL),
    border: Border.all(
      color: isSelected ? (selectedColor ?? designPrimaryColor) : borderColor,
      width: isSelected ? 2 : 1,
    ),
  );

  /// 图片容器样式
  static BoxDecoration get imageContainerDecoration => BoxDecoration(
    color: backgroundColor,
    borderRadius: BorderRadius.circular(radiusS),
    border: Border.all(color: borderColor),
  );
}
