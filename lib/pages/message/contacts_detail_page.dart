import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/contact_category.dart';
import '../../models/user.dart';
import '../../services/service_locator.dart';
import 'conversation_detail_page.dart';
import 'message_search_page.dart';
import '../../models/message/conversation.dart';

/// 联系人详情页面
class ContactsDetailPage extends StatefulWidget {
  final ContactCategory category;

  const ContactsDetailPage({super.key, required this.category});

  @override
  State<ContactsDetailPage> createState() => _ContactsDetailPageState();
}

class _ContactsDetailPageState extends State<ContactsDetailPage> {
  List<User> _contacts = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadContacts();
  }

  /// 加载联系人数据
  Future<void> _loadContacts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 从服务获取真实数据
      final contacts = await Services.contact.getContactsByCategory(
        widget.category.type,
      );

      if (mounted) {
        setState(() {
          _contacts = contacts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
      }
    }
  }

  /// 获取过滤后的联系人列表
  List<User> get _filteredContacts {
    if (_searchQuery.isEmpty) {
      return _contacts;
    }
    return _contacts.where((contact) {
      return contact.username.toLowerCase().contains(
        _searchQuery.toLowerCase(),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Text(
          widget.category.name,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        actions: [
          if (widget.category.type != ContactCategoryType.blocked)
            IconButton(
              icon: const FaIcon(FontAwesomeIcons.userPlus, size: 18),
              onPressed: _showAddContactOptions,
            ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          if (_contacts.isNotEmpty)
            Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Container(
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(18),
                ),
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: '搜索联系人',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    prefixIcon: Icon(Icons.search, size: 20),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),

          // 联系人列表
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredContacts.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                      onRefresh: _loadContacts,
                      child: ListView.separated(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        itemCount: _filteredContacts.length,
                        separatorBuilder:
                            (context, index) => const Divider(
                              height: 1,
                              indent: 72,
                              endIndent: 16,
                            ),
                        itemBuilder: (context, index) {
                          return _buildContactItem(_filteredContacts[index]);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  /// 联系人列表项
  Widget _buildContactItem(User contact) {
    return Container(
      color: Colors.white,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: Colors.grey[300],
          backgroundImage:
              contact.avatar != null ? NetworkImage(contact.avatar!) : null,
          child:
              contact.avatar == null
                  ? Text(
                    contact.username.isNotEmpty ? contact.username[0] : '?',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                  : null,
        ),
        title: Text(
          contact.username,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          _getContactSubtitle(contact),
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        trailing: _buildContactActions(contact),
        onTap: () => _showContactProfile(contact),
      ),
    );
  }

  /// 获取联系人副标题
  String _getContactSubtitle(User contact) {
    switch (widget.category.type) {
      case ContactCategoryType.friends:
        return '互相关注';
      case ContactCategoryType.following:
        return '已关注';
      case ContactCategoryType.followers:
        return '关注了你';
      case ContactCategoryType.blocked:
        return '已拉黑';
    }
  }

  /// 联系人操作按钮
  Widget _buildContactActions(User contact) {
    switch (widget.category.type) {
      case ContactCategoryType.friends:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildActionButton(
              '发消息',
              Theme.of(context).primaryColor,
              () => _sendMessage(contact),
            ),
          ],
        );
      case ContactCategoryType.following:
        return _buildActionButton(
          '取消关注',
          Colors.grey,
          () => _unfollowUser(contact),
        );
      case ContactCategoryType.followers:
        return _buildActionButton(
          '回关',
          Theme.of(context).primaryColor,
          () => _followUser(contact),
        );
      case ContactCategoryType.blocked:
        return _buildActionButton(
          '解除拉黑',
          Colors.orange,
          () => _unblockUser(contact),
        );
    }
  }

  /// 操作按钮
  Widget _buildActionButton(String text, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// 空状态
  Widget _buildEmptyState() {
    String emptyText;
    String emptySubtext;
    IconData emptyIcon;

    switch (widget.category.type) {
      case ContactCategoryType.friends:
        emptyText = '暂无好友';
        emptySubtext = '去关注一些钓友吧';
        emptyIcon = FontAwesomeIcons.userGroup;
        break;
      case ContactCategoryType.following:
        emptyText = '暂无关注';
        emptySubtext = '去发现更多有趣的钓友';
        emptyIcon = FontAwesomeIcons.eye;
        break;
      case ContactCategoryType.followers:
        emptyText = '暂无粉丝';
        emptySubtext = '分享更多钓鱼经验吸引粉丝';
        emptyIcon = FontAwesomeIcons.user;
        break;
      case ContactCategoryType.blocked:
        emptyText = '黑名单为空';
        emptySubtext = '没有被拉黑的用户';
        emptyIcon = FontAwesomeIcons.ban;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(emptyIcon, size: 64, color: Colors.grey[300]),
          const SizedBox(height: 16),
          Text(
            emptyText,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            emptySubtext,
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }

  /// 显示添加联系人选项
  void _showAddContactOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.qrcode),
                  title: const Text('扫一扫'),
                  subtitle: const Text('扫描二维码添加好友'),
                  onTap: () {
                    Navigator.pop(context);
                    _scanQRCode();
                  },
                ),
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.magnifyingGlass),
                  title: const Text('搜索添加'),
                  subtitle: const Text('通过用户名或手机号搜索'),
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToSearch();
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// 扫码功能
  void _scanQRCode() async {
    try {
      // 这里应该使用qr_code_scanner插件来扫描二维码
      // final result = await Navigator.push(
      //   context,
      //   MaterialPageRoute(builder: (context) => QRScannerPage()),
      // );

      // 暂时显示提示
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('扫码功能需要安装qr_code_scanner插件')),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('扫码失败: $e')));
    }
  }

  /// 跳转到搜索页面
  void _navigateToSearch() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const MessageSearchPage()),
    );
  }

  /// 显示联系人资料
  void _showContactProfile(User contact) {
    // 跳转到用户资料页面
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('查看 ${contact.username} 的资料')));
  }

  /// 发送消息
  void _sendMessage(User contact) {
    // 创建对话并跳转
    final conversation = Conversation(
      id: 'conv_${contact.id}',
      otherUserId: contact.id,
      otherUserName: contact.username,
      otherUserAvatar: contact.avatar,
      lastMessage: null,
      lastMessageTime: null,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ConversationDetailPage(conversation: conversation),
      ),
    );
  }

  /// 关注用户
  void _followUser(User contact) async {
    try {
      // 调用关注服务
      final success = await Services.social.followUser(contact.id);

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('已关注 ${contact.username}')));
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('关注失败，请重试')));
      }

      // 刷新列表
      _loadContacts();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('关注失败: $e')));
    }
  }

  /// 取消关注用户
  void _unfollowUser(User contact) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('取消关注'),
            content: Text('确定要取消关注 ${contact.username} 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('确定'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        // 调用取消关注服务
        final success = await Services.social.unfollowUser(contact.id);

        if (!mounted) return;

        if (success) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('已取消关注 ${contact.username}')));
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('取消关注失败，请重试')));
        }

        // 刷新列表
        _loadContacts();
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }

  /// 解除拉黑用户
  void _unblockUser(User contact) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('解除拉黑'),
            content: Text('确定要解除拉黑 ${contact.username} 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('确定'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        // 调用解除拉黑服务
        final success = await Services.contact.unblockUser(contact.id);

        if (!mounted) return;

        if (success) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('已解除拉黑 ${contact.username}')));
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('解除拉黑失败，请重试')));
        }

        // 刷新列表
        _loadContacts();
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }
}
