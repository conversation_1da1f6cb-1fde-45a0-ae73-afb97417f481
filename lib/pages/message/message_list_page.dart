import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/conversation.dart';
import '../../services/service_locator.dart';
import 'conversation_detail_page.dart';
import 'notification_page.dart';

/// 消息列表页面
class MessageListPage extends StatefulWidget {
  const MessageListPage({super.key});

  @override
  State<MessageListPage> createState() => _MessageListPageState();
}

class _MessageListPageState extends State<MessageListPage>
    with AutomaticKeepAliveClientMixin {
  List<Conversation> _conversations = [];
  bool _isLoading = true;
  int _systemNotificationCount = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _setupMessageService();
    _loadConversations();

    // 启动消息服务轮询（如果尚未启动）
    _ensureMessageServiceRunning();
  }

  /// 确保消息服务正在运行
  void _ensureMessageServiceRunning() {
    if (!Services.message.isPolling) {
      Services.message.startPolling();
    }
  }

  @override
  void dispose() {
    // 移除监听器
    Services.message.conversationsNotifier.removeListener(_onConversationsChanged);
    super.dispose();
  }

  /// 设置消息服务监听
  void _setupMessageService() {
    // 监听对话列表变化
    Services.message.conversationsNotifier.addListener(_onConversationsChanged);

    // 如果服务已经有数据，立即更新
    final currentConversations = Services.message.conversationsNotifier.value;
    if (currentConversations.isNotEmpty) {
      setState(() {
        _conversations = currentConversations;
        _isLoading = false;
      });
    }
  }

  /// 对话列表变化处理
  void _onConversationsChanged() {
    if (mounted) {
      setState(() {
        _conversations = Services.message.conversationsNotifier.value;
        _systemNotificationCount = _getSystemNotificationCount();
        _isLoading = false;
      });
    }
  }

  /// 获取系统通知数量
  int _getSystemNotificationCount() {
    // 这里可以从NotificationService获取实际的系统通知数量
    // 目前返回模拟数据
    try {
      // 可以从Services.notification获取实际通知数量
      return 3; // 模拟数据
    } catch (e) {
      return 0;
    }
  }

  /// 加载对话列表
  Future<void> _loadConversations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 使用MessageService获取对话列表
      await Services.message.refreshMessages();

      if (mounted) {
        setState(() {
          _conversations = Services.message.conversationsNotifier.value;
          _systemNotificationCount = _getSystemNotificationCount();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // 显示用户友好的错误信息
        String errorMessage = '网络连接失败，请检查网络设置';
        if (e.toString().contains('401') || e.toString().contains('unauthorized')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (e.toString().contains('timeout')) {
          errorMessage = '网络超时，请稍后重试';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: const Color(0xFFFF3B30),
            action: SnackBarAction(
              label: '重试',
              textColor: Colors.white,
              onPressed: _loadConversations,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF0088FF)),
      );
    }

    return Container(
      color: Colors.white,
      child: RefreshIndicator(
        onRefresh: _loadConversations,
        color: const Color(0xFF07C160),
        child: Column(
          children: [
            // 系统通知入口
            _buildSystemNotificationEntry(),

            // 对话列表
            Expanded(
              child:
                  _conversations.isEmpty
                      ? _buildEmptyState()
                      : ListView.separated(
                        padding: EdgeInsets.zero,
                        itemCount: _conversations.length,
                        separatorBuilder:
                            (context, index) => const Divider(
                              height: 1,
                              thickness: 0.5,
                              color: Color(0xFFE5E5E5),
                              indent: 68,
                            ),
                        itemBuilder: (context, index) {
                          return _buildConversationItem(_conversations[index]);
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  /// 系统通知入口（微信风格）
  Widget _buildSystemNotificationEntry() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 0.5,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NotificationPage()),
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // 系统通知图标
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B6B),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.notifications_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),

                // 内容区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题和时间
                      Row(
                        children: [
                          const Expanded(
                            child: Text(
                              '系统信息',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF181818),
                              ),
                            ),
                          ),
                          Text(
                            '20:22',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),

                      // 消息预览
                      Text(
                        _systemNotificationCount > 0
                            ? '[$_systemNotificationCount条] 财经三分钟: 1000万一晚！撩边"团播"杀疯…'
                            : '暂无新通知',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // 未读角标
                if (_systemNotificationCount > 0)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFF3B30),
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 对话列表项（微信风格）
  Widget _buildConversationItem(Conversation conversation) {
    return Container(
      color: Colors.white,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: conversation.isPending
              ? null
              : () => _openConversation(conversation),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // 头像
                CircleAvatar(
                  radius: 24,
                  backgroundColor: const Color(0xFFF0F0F0),
                  backgroundImage: conversation.otherUserAvatar != null
                      ? NetworkImage(conversation.otherUserAvatar!)
                      : null,
                  child: conversation.otherUserAvatar == null
                      ? Text(
                          conversation.otherUserName.isNotEmpty
                              ? conversation.otherUserName[0]
                              : '?',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF999999),
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),

                // 内容区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户名和时间
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation.otherUserName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF181818),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (conversation.isPending)
                            Container(
                              margin: const EdgeInsets.only(right: 8),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFF3B30),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Text(
                                '陌生人',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          Text(
                            conversation.displayTime,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),

                      // 消息预览
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation.displayLastMessage,
                              style: TextStyle(
                                fontSize: 14,
                                color: conversation.unreadCount > 0
                                    ? Colors.grey[700]
                                    : Colors.grey[600],
                                fontWeight: conversation.unreadCount > 0
                                    ? FontWeight.w400
                                    : FontWeight.normal,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          // 未读角标
                          if (conversation.unreadCount > 0 && !conversation.isPending)
                            Container(
                              margin: const EdgeInsets.only(left: 8),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: const BoxDecoration(
                                color: Color(0xFFFF3B30),
                                shape: BoxShape.circle,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 18,
                                minHeight: 18,
                              ),
                              child: Text(
                                conversation.unreadCount > 99
                                    ? '99+'
                                    : conversation.unreadCount.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                        ],
                      ),

                      // 陌生人操作按钮
                      if (conversation.isPending) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            _buildWeChatActionButton(
                              '接受',
                              const Color(0xFF07C160),
                              () => _acceptConversation(conversation),
                            ),
                            const SizedBox(width: 8),
                            _buildWeChatActionButton(
                              '拒绝',
                              Colors.grey[400]!,
                              () => _rejectConversation(conversation),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 微信风格操作按钮
  Widget _buildWeChatActionButton(
    String text,
    Color color,
    VoidCallback onPressed,
  ) {
    return Expanded(
      child: Container(
        height: 32,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(4),
            onTap: onPressed,
            child: Center(
              child: Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(FontAwesomeIcons.message, size: 64, color: Colors.grey[300]),
          const SizedBox(height: 16),
          Text(
            '暂无消息',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始与钓友们交流吧',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }

  /// 打开对话
  void _openConversation(Conversation conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ConversationDetailPage(conversation: conversation),
      ),
    ).then((_) {
      // 返回时刷新列表
      _loadConversations();
    });
  }

  /// 接受陌生人对话
  void _acceptConversation(Conversation conversation) async {
    try {
      // 调用联系人服务接受对话
      final success = await Services.contact.acceptConversation(
        conversation.id,
      );

      if (success) {
        // 刷新对话列表
        await Services.message.refreshMessages();

        // 跳转到对话页面
        final updatedConversation = Services.message.conversationsNotifier.value
            .firstWhere((c) => c.id == conversation.id);
        _openConversation(updatedConversation);
      } else {
        throw Exception('接受对话失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }

  /// 拒绝陌生人对话
  void _rejectConversation(Conversation conversation) async {
    try {
      // 调用服务拒绝对话并拉黑用户
      final success = await Services.contact.rejectAndBlockConversation(
        conversation.id,
        conversation.otherUserId,
      );

      if (!mounted) return;

      if (success) {
        // 从列表中移除
        setState(() {
          _conversations.removeWhere((c) => c.id == conversation.id);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已拒绝 ${conversation.otherUserName} 的消息')),
        );
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('操作失败，请重试')));
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
    }
  }
}
