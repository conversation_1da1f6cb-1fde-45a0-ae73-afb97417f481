import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'message_list_page.dart';
import 'message_search_page.dart';
import 'contacts_list_page.dart';

/// 消息主页面
class MessageMainPage extends StatefulWidget {
  const MessageMainPage({super.key});

  @override
  State<MessageMainPage> createState() => _MessageMainPageState();
}

class _MessageMainPageState extends State<MessageMainPage> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F7F7),
      body: SafeArea(
        child: Column(
          children: [
            // 微信风格AppBar
            _buildCustomAppBar(),

            // 消息列表内容
            const Expanded(
              child: MessageListPage(),
            ),
          ],
        ),
      ),
    );
  }

  /// 微信风格AppBar
  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧联系人按钮
          SizedBox(
            width: 44,
            height: 44,
            child: IconButton(
              icon: const Icon(
                Icons.contacts,
                size: 22,
                color: Color(0xFF181818),
              ),
              onPressed: _showContactsDrawer,
            ),
          ),
          // 标题居中
          Expanded(
            child: Text(
              '微信',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF181818),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // 右侧加号按钮
          SizedBox(
            width: 44,
            height: 44,
            child: IconButton(
              icon: const Icon(
                Icons.add,
                size: 24,
                color: Color(0xFF181818),
              ),
              onPressed: _showAddOptions,
            ),
          ),
        ],
      ),
    );
  }



  /// 显示添加选项
  void _showAddOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 36,
                height: 4,
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              _buildAddOptionItem(
                icon: FontAwesomeIcons.userPlus,
                title: '添加朋友',
                onTap: () {
                  Navigator.pop(context);
                  _showSearch();
                },
              ),
              _buildAddOptionItem(
                icon: FontAwesomeIcons.qrcode,
                title: '扫一扫',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 实现扫码功能
                },
              ),
              _buildAddOptionItem(
                icon: FontAwesomeIcons.users,
                title: '发起群聊',
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 实现群聊功能
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建添加选项项
  Widget _buildAddOptionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: const Color(0xFF07C160),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
    );
  }

  /// 显示联系人抽屉
  void _showContactsDrawer() {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.85,
              height: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(2, 0),
                  ),
                ],
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // 联系人页面标题栏
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFE5E5E5),
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Text(
                            '联系人',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF181818),
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(
                              Icons.close,
                              color: Color(0xFF181818),
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ],
                      ),
                    ),
                    // 联系人列表内容
                    const Expanded(
                      child: ContactsListPage(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 显示搜索页面
  void _showSearch() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const MessageSearchPage()),
    );
  }
}
