import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/contact_category.dart';
import '../../services/service_locator.dart';
import 'contacts_detail_page.dart';

/// 联系人列表页面
class ContactsListPage extends StatefulWidget {
  const ContactsListPage({super.key});

  @override
  State<ContactsListPage> createState() => _ContactsListPageState();
}

class _ContactsListPageState extends State<ContactsListPage>
    with AutomaticKeepAliveClientMixin {
  List<ContactCategory> _categories = [];
  bool _isLoading = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadContactCategories();
  }

  /// 加载联系人分类数据
  Future<void> _loadContactCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 从服务获取真实数据
      final categories = await Services.contact.getContactCategories();

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF0088FF)),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadContactCategories,
      color: const Color(0xFF0088FF),
      child:
          _categories.isEmpty
              ? _buildEmptyState()
              : ListView.separated(
                padding: const EdgeInsets.all(16),
                itemCount: _categories.length,
                separatorBuilder:
                    (context, index) => const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  return _buildCategoryItem(_categories[index]);
                },
              ),
    );
  }

  /// 联系人分类项
  Widget _buildCategoryItem(ContactCategory category) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _openCategoryDetail(category),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // 图标容器
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _getCategoryGradient(category.type),
                    ),
                    borderRadius: BorderRadius.circular(28),
                    boxShadow: [
                      BoxShadow(
                        color: _getCategoryColor(
                          category.type,
                        ).withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(child: _getCategoryIcon(category.type)),
                ),

                // 内容区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题行
                      Row(
                        children: [
                          Text(
                            category.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF212529),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (category.count > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF8F9FA),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: const Color(0xFFE9ECEF),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                category.displayCount,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF6C757D),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 6),

                      // 描述
                      Text(
                        _getCategoryDescription(category.type),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6C757D),
                        ),
                      ),
                    ],
                  ),
                ),

                // 右侧内容
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (category.showBadge && category.badgeCount > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          category.badgeCount > 99
                              ? '99+'
                              : category.badgeCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    const Icon(
                      Icons.arrow_forward_ios_rounded,
                      size: 16,
                      color: Color(0xFF6C757D),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return const Color(0xFF0088FF);
      case ContactCategoryType.following:
        return const Color(0xFF28A745);
      case ContactCategoryType.followers:
        return const Color(0xFFFF8E53);
      case ContactCategoryType.blocked:
        return const Color(0xFFDC3545);
    }
  }

  /// 获取分类渐变色
  List<Color> _getCategoryGradient(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return [const Color(0xFF0088FF), const Color(0xFF00D4AA)];
      case ContactCategoryType.following:
        return [const Color(0xFF28A745), const Color(0xFF20C997)];
      case ContactCategoryType.followers:
        return [const Color(0xFFFF8E53), const Color(0xFFFF6B6B)];
      case ContactCategoryType.blocked:
        return [const Color(0xFFDC3545), const Color(0xFFE74C3C)];
    }
  }

  /// 获取分类图标
  Widget _getCategoryIcon(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return const FaIcon(
          FontAwesomeIcons.userGroup,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.following:
        return const FaIcon(
          FontAwesomeIcons.eye,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.followers:
        return const FaIcon(
          FontAwesomeIcons.user,
          color: Colors.white,
          size: 20,
        );
      case ContactCategoryType.blocked:
        return const FaIcon(
          FontAwesomeIcons.ban,
          color: Colors.white,
          size: 20,
        );
    }
  }

  /// 获取分类描述
  String _getCategoryDescription(ContactCategoryType type) {
    switch (type) {
      case ContactCategoryType.friends:
        return '互相关注的钓友';
      case ContactCategoryType.following:
        return '我关注的用户';
      case ContactCategoryType.followers:
        return '关注我的用户';
      case ContactCategoryType.blocked:
        return '已拉黑的用户';
    }
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.addressBook,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无联系人',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '去发现更多钓友吧',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }

  /// 打开分类详情
  void _openCategoryDetail(ContactCategory category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ContactsDetailPage(category: category),
      ),
    ).then((_) {
      // 返回时刷新数据
      _loadContactCategories();
    });
  }
}
