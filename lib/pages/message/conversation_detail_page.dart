import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/conversation.dart';
import '../../models/message/message.dart';
import '../../services/service_locator.dart';

/// 对话详情页面
class ConversationDetailPage extends StatefulWidget {
  final Conversation conversation;

  const ConversationDetailPage({super.key, required this.conversation});

  @override
  State<ConversationDetailPage> createState() => _ConversationDetailPageState();
}

class _ConversationDetailPageState extends State<ConversationDetailPage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Message> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载消息历史
  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: 从服务获取真实消息数据
      await Future.delayed(const Duration(milliseconds: 500));

      // 使用MessageService获取消息
      final messages = await Services.message.getMessages(
        widget.conversation.id,
      );

      if (mounted) {
        setState(() {
          _messages = messages;
          _isLoading = false;
        });

        // 滚动到底部
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载消息失败: $e')));
      }
    }
  }

  /// 发送消息
  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    try {
      // 清空输入框
      _messageController.clear();

      // 使用MessageService发送消息
      final message = await Services.message.sendMessage(
        widget.conversation.otherUserId,
        content,
      );

      if (message != null) {
        // 刷新消息列表
        await _loadMessages();

        // 滚动到底部
        _scrollToBottom();
      } else {
        throw Exception('发送失败');
      }

      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发送失败: $e')));
      }
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.grey[300],
              backgroundImage:
                  widget.conversation.otherUserAvatar != null
                      ? NetworkImage(widget.conversation.otherUserAvatar!)
                      : null,
              child:
                  widget.conversation.otherUserAvatar == null
                      ? Text(
                        widget.conversation.otherUserName.isNotEmpty
                            ? widget.conversation.otherUserName[0]
                            : '?',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.conversation.otherUserName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _getOnlineStatus(),
                    style: TextStyle(fontSize: 12, color: Colors.green[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.phone, size: 18),
            onPressed: () {
              // TODO: 实现语音通话功能
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('语音通话功能待开发')));
            },
          ),
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.ellipsisVertical, size: 18),
            onPressed: _showMoreOptions,
          ),
        ],
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        return _buildMessageBubble(_messages[index]);
                      },
                    ),
          ),

          // 输入区域
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 获取在线状态
  String _getOnlineStatus() {
    // 简单的在线状态逻辑，可以后续扩展为真实的在线状态检测
    final now = DateTime.now();
    final hour = now.hour;

    // 根据时间段显示不同状态
    if (hour >= 6 && hour < 12) {
      return '上午在线';
    } else if (hour >= 12 && hour < 18) {
      return '下午在线';
    } else if (hour >= 18 && hour < 22) {
      return '晚上在线';
    } else {
      return '最近在线';
    }
  }

  /// 显示用户资料
  void _showUserProfile() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('${widget.conversation.otherUserName} 的资料'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 头像和基本信息
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage:
                            widget.conversation.otherUserAvatar != null
                                ? NetworkImage(
                                  widget.conversation.otherUserAvatar!,
                                )
                                : null,
                        child:
                            widget.conversation.otherUserAvatar == null
                                ? Text(
                                  widget.conversation.otherUserName.isNotEmpty
                                      ? widget.conversation.otherUserName[0]
                                      : '?',
                                  style: const TextStyle(fontSize: 24),
                                )
                                : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.conversation.otherUserName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getOnlineStatus(),
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  // 用户信息
                  const Text(
                    '用户信息',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow('注册时间', '2024年1月'),
                  _buildInfoRow('发布钓点', '12个'),
                  _buildInfoRow('获得点赞', '156个'),
                  _buildInfoRow('粉丝数', '23人'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  /// 切换消息免打扰
  void _toggleMuteNotification() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('消息免打扰'),
            content: const Text('开启后，您将不会收到此对话的消息通知'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  try {
                    // 这里可以调用服务来设置免打扰
                    // await Services.message.muteConversation(widget.conversation.id);

                    if (!mounted) return;
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(const SnackBar(content: Text('已开启消息免打扰')));
                  } catch (e) {
                    if (!mounted) return;
                    ScaffoldMessenger.of(
                      context,
                    ).showSnackBar(SnackBar(content: Text('设置失败: $e')));
                  }
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 拍照功能
  void _takePhoto() async {
    try {
      // 这里应该使用image_picker插件来拍照
      // final ImagePicker picker = ImagePicker();
      // final XFile? photo = await picker.pickImage(source: ImageSource.camera);

      // 暂时显示提示
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('拍照功能需要安装image_picker插件')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('拍照失败: $e')));
    }
  }

  /// 选择图片功能
  void _pickImage() async {
    try {
      // 这里应该使用image_picker插件来选择图片
      // final ImagePicker picker = ImagePicker();
      // final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      // 暂时显示提示
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('选择图片功能需要安装image_picker插件')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('选择图片失败: $e')));
    }
  }

  /// 发送位置功能
  void _sendLocation() async {
    try {
      // 这里应该使用geolocator插件来获取位置
      // final Position position = await Geolocator.getCurrentPosition();

      // 暂时显示提示
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('发送位置功能需要安装geolocator插件')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('获取位置失败: $e')));
    }
  }

  /// 消息气泡
  Widget _buildMessageBubble(Message message) {
    final currentUserId = Services.auth.currentUser?.id;
    final isMe = currentUserId != null && message.senderId == currentUserId;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[300],
              backgroundImage:
                  widget.conversation.otherUserAvatar != null
                      ? NetworkImage(widget.conversation.otherUserAvatar!)
                      : null,
              child:
                  widget.conversation.otherUserAvatar == null
                      ? Text(
                        widget.conversation.otherUserName.isNotEmpty
                            ? widget.conversation.otherUserName[0]
                            : '?',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 8),
          ],

          Flexible(
            child: Column(
              crossAxisAlignment:
                  isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: isMe ? Theme.of(context).primaryColor : Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(20),
                      topRight: const Radius.circular(20),
                      bottomLeft: Radius.circular(isMe ? 20 : 4),
                      bottomRight: Radius.circular(isMe ? 4 : 20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    message.content,
                    style: TextStyle(
                      fontSize: 15,
                      color: isMe ? Colors.white : Colors.black87,
                      height: 1.3,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _formatMessageTime(message.createdAt),
                      style: TextStyle(fontSize: 11, color: Colors.grey[500]),
                    ),
                    if (isMe) ...[
                      const SizedBox(width: 4),
                      Icon(
                        message.isRead ? Icons.done_all : Icons.done,
                        size: 14,
                        color: message.isRead ? Colors.blue : Colors.grey[500],
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          if (isMe) const SizedBox(width: 8),
        ],
      ),
    );
  }

  /// 输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.plus, size: 20),
            onPressed: _showAttachmentOptions,
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: '输入消息...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color:
                    _isSending ? Colors.grey : Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child:
                  _isSending
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.send, color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化消息时间
  String _formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  /// 显示更多选项
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.user),
                  title: const Text('查看资料'),
                  onTap: () {
                    Navigator.pop(context);
                    _showUserProfile();
                  },
                ),
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.bell),
                  title: const Text('消息免打扰'),
                  onTap: () {
                    Navigator.pop(context);
                    _toggleMuteNotification();
                  },
                ),
                ListTile(
                  leading: const FaIcon(
                    FontAwesomeIcons.ban,
                    color: Colors.red,
                  ),
                  title: const Text(
                    '拉黑用户',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showBlockUserDialog();
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// 显示附件选项
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAttachmentOption(
                      icon: FontAwesomeIcons.camera,
                      label: '拍照',
                      onTap: () {
                        Navigator.pop(context);
                        _takePhoto();
                      },
                    ),
                    _buildAttachmentOption(
                      icon: FontAwesomeIcons.image,
                      label: '相册',
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage();
                      },
                    ),
                    _buildAttachmentOption(
                      icon: FontAwesomeIcons.locationDot,
                      label: '位置',
                      onTap: () {
                        Navigator.pop(context);
                        _sendLocation();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  /// 附件选项
  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(height: 8),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  /// 显示拉黑用户对话框
  void _showBlockUserDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('拉黑用户'),
            content: Text('确定要拉黑 ${widget.conversation.otherUserName} 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();

                  try {
                    // 实现拉黑用户功能
                    final success = await Services.contact.blockUser(
                      widget.conversation.otherUserId,
                    );

                    if (!mounted) return;

                    if (success) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            '已拉黑 ${widget.conversation.otherUserName}',
                          ),
                        ),
                      );
                      navigator.pop(); // 返回消息列表
                    } else {
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(content: Text('拉黑失败，请重试'))
                      );
                    }
                  } catch (e) {
                    if (!mounted) return;
                    scaffoldMessenger.showSnackBar(
                      SnackBar(content: Text('拉黑失败: $e'))
                    );
                  }
                },
                child: const Text('确定', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}
