import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../services/service_locator.dart';

/// 系统通知页面
class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  List<SystemNotification> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// 加载系统通知
  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 从服务获取真实数据
      final notifications = await Services.notification.getNotifications();

      if (mounted) {
        setState(() {
          _notifications = notifications;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
      }
    }
  }

  /// 标记通知为已读
  Future<void> _markAsRead(SystemNotification notification) async {
    if (notification.isRead) return;

    try {
      // 调用服务标记为已读
      await Services.notification.markAsRead(notification.id);

      if (!mounted) return;

      setState(() {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = notification.copyWith(isRead: true);
        }
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
    }
  }

  /// 标记所有通知为已读
  Future<void> _markAllAsRead() async {
    try {
      // 调用服务标记所有为已读
      await Services.notification.markAllAsRead();

      if (!mounted) return;

      setState(() {
        _notifications =
            _notifications.map((n) => n.copyWith(isRead: true)).toList();
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('已标记所有通知为已读')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
    }
  }

  /// 删除通知
  Future<void> _deleteNotification(SystemNotification notification) async {
    try {
      // 调用服务删除通知
      await Services.notification.deleteNotification(notification.id);

      if (!mounted) return;

      setState(() {
        _notifications.removeWhere((n) => n.id == notification.id);
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('通知已删除')));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('删除失败: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notifications.where((n) => !n.isRead).length;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: const Text(
          '系统通知',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        actions: [
          if (unreadCount > 0)
            TextButton(onPressed: _markAllAsRead, child: const Text('全部已读')),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _notifications.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                onRefresh: _loadNotifications,
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: _notifications.length,
                  separatorBuilder:
                      (context, index) =>
                          const Divider(height: 1, indent: 72, endIndent: 16),
                  itemBuilder: (context, index) {
                    return _buildNotificationItem(_notifications[index]);
                  },
                ),
              ),
    );
  }

  /// 通知列表项
  Widget _buildNotificationItem(SystemNotification notification) {
    return Container(
      color: notification.isRead ? Colors.white : Colors.blue.shade50,
      child: Dismissible(
        key: Key(notification.id),
        direction: DismissDirection.endToStart,
        background: Container(
          color: Colors.red,
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 16),
          child: const Icon(Icons.delete, color: Colors.white),
        ),
        onDismissed: (direction) {
          _deleteNotification(notification);
        },
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getNotificationColor(notification.type),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              _getNotificationIcon(notification.type),
              color: Colors.white,
              size: 20,
            ),
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  notification.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight:
                        notification.isRead ? FontWeight.w500 : FontWeight.w600,
                  ),
                ),
              ),
              if (!notification.isRead)
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.content,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                _formatTime(notification.createdAt),
                style: TextStyle(fontSize: 12, color: Colors.grey[500]),
              ),
            ],
          ),
          onTap: () {
            _markAsRead(notification);
            _showNotificationDetail(notification);
          },
        ),
      ),
    );
  }

  /// 获取通知类型颜色
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return Colors.blue;
      case NotificationType.like:
        return Colors.red;
      case NotificationType.follow:
        return Colors.green;
      case NotificationType.comment:
        return Colors.orange;
      case NotificationType.update:
        return Colors.purple;
    }
  }

  /// 获取通知类型图标
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return FontAwesomeIcons.bell;
      case NotificationType.like:
        return FontAwesomeIcons.heart;
      case NotificationType.follow:
        return FontAwesomeIcons.userPlus;
      case NotificationType.comment:
        return FontAwesomeIcons.comment;
      case NotificationType.update:
        return FontAwesomeIcons.download;
    }
  }

  /// 格式化时间
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.month}月${dateTime.day}日';
    }
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(FontAwesomeIcons.bell, size: 64, color: Colors.grey[300]),
          const SizedBox(height: 16),
          Text(
            '暂无通知',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '有新通知时会在这里显示',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }

  /// 显示通知详情
  void _showNotificationDetail(SystemNotification notification) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(notification.title),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    notification.content,
                    style: const TextStyle(fontSize: 16, height: 1.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '时间: ${_formatTime(notification.createdAt)}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }
}

/// 系统通知模型
class SystemNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String content;
  final DateTime createdAt;
  final bool isRead;

  SystemNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.content,
    required this.createdAt,
    this.isRead = false,
  });

  SystemNotification copyWith({
    String? id,
    NotificationType? type,
    String? title,
    String? content,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return SystemNotification(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }
}

/// 通知类型枚举
enum NotificationType {
  system, // 系统通知
  like, // 点赞通知
  follow, // 关注通知
  comment, // 评论通知
  update, // 更新通知
}
