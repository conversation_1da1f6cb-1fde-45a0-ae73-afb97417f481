import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'map_page.dart';
import 'recommend_page.dart';
import 'message/message_main_page.dart';
import 'profile_page.dart';
import 'publish_dynamic_page.dart';
import '../services/service_locator.dart';

import '../theme/app_theme_manager.dart';
import '../widgets/custom_bottom_navigation_bar.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  int _currentIndex = 0;
  bool _showBottomNavigationBar = true; // 控制底部导航栏显示

  // GlobalKey用于访问MapPage实例
  final GlobalKey<State<MapPage>> _mapPageKey = GlobalKey<State<MapPage>>();

  // 页面列表
  late final List<Widget> _pages;

  // 使用新的服务架构
  // 通过Services便捷访问器访问认证服务

  @override
  bool get wantKeepAlive => true; // 保持MainScreen状态，避免应用恢复时重建

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 直接创建页面列表
    _pages = [
      MapPage(
        key: _mapPageKey,
        onBottomNavigationBarVisibilityChanged: (visible) {
  
          // 只有当前显示的是MapPage（index=0）时才处理导航栏隐藏
          if (_currentIndex == 0) {
            setState(() {
              _showBottomNavigationBar = visible;
            });
          }
        },
      ),
      const RecommendPage(),
      const MessageMainPage(),
      const ProfilePage(),
    ];
  }

  /// 处理底部导航栏点击事件
  void _handleBottomNavTap(int index) {
    if (index == -1) {
      // 点击添加按钮
      _handleAddButtonTap();
      return;
    }

    // 处理正常的页面切换
    setState(() {
      final oldIndex = _currentIndex;
      _currentIndex = index;

      // 如果从MapPage切换到其他页面，确保显示导航栏
      if (oldIndex == 0 && index != 0) {
        _showBottomNavigationBar = true;
      }
    });
  }

  /// 处理添加按钮点击事件
  void _handleAddButtonTap() {
    switch (_currentIndex) {
      case 0: // 地图页面
        _showAddModeSelectionDialog();
        break;
      case 1: // 推荐页面
        _navigateToPublishPost();
        break;
      case 2: // 信息页面
        _showMessageAddOptions();
        break;
      case 3: // 我的页面
        _showMyPageAddOptions();
        break;
    }
  }

  /// 导航到发布动态页面
  void _navigateToPublishPost() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PublishDynamicPage()),
    ).then((result) {
      if (result == true) {
        // 发布成功，可以刷新推荐页面
        debugPrint('动态发布成功');
      }
    });
  }

  /// 显示信息页面添加选项
  void _showMessageAddOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 36,
                    height: 4,
                    margin: const EdgeInsets.only(top: 8, bottom: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  _buildAddOptionItem(
                    icon: Icons.person_add,
                    title: '添加好友',
                    subtitle: '通过搜索添加新朋友',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: 实现添加好友功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('添加好友功能待实现')),
                      );
                    },
                  ),
                  _buildAddOptionItem(
                    icon: Icons.group_add,
                    title: '添加群聊',
                    subtitle: '创建或加入群组聊天',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: 实现添加群聊功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('添加群聊功能待实现')),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  /// 显示我的页面添加选项
  void _showMyPageAddOptions() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('我的页面暂无添加功能')));
  }

  /// 显示添加模式选择底部面板
  void _showAddModeSelectionDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖拽指示器
              Container(
                width: 36,
                height: 4,
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // // 标题
              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              //   child: Text(
              //     '选择发布类型',
              //     style: TextStyle(
              //       fontSize: 18,
              //       fontWeight: FontWeight.w600,
              //       color: Theme.of(context).colorScheme.onSurface,
              //     ),
              //   ),
              // ),
              const SizedBox(height: 8),
              // 添加钓点选项
              _buildAddOptionItem(
                icon: Icons.location_on,
                title: '添加钓点',
                subtitle: '分享您发现的好钓点',
                onTap: () {
                  _handleAddSpotSelection(context);
                },
              ),
              // 约钓活动选项
              _buildAddOptionItem(
                icon: Icons.group,
                title: '约钓活动',
                subtitle: '发起钓鱼聚会活动',
                onTap: () {
                  _handleAddActivitySelection(context);
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  /// 触发添加钓点模式
  void _triggerAddSpotMode() {
    // 确保当前在主页，如果不在主页则先切换到主页
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      // 等待页面切换完成后再调用添加钓点功能
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _callMapPageAddSpot();
      });
    } else {
      // 已经在主页，直接调用添加钓点功能
      _callMapPageAddSpot();
    }
  }

  /// 触发添加活动模式
  void _triggerAddActivityMode() {
    debugPrint('🔍 [MainScreen] 触发添加活动模式');

    // 确保当前在主页，如果不在主页则先切换到主页
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      // 等待页面切换完成后再调用添加活动功能
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _callMapPageAddActivity();
      });
    } else {
      // 已经在主页，直接调用添加活动功能
      _callMapPageAddActivity();
    }
  }

  /// 调用MapPage的添加钓点功能
  void _callMapPageAddSpot() {
    final mapPageState = _mapPageKey.currentState;
    if (mapPageState != null) {
      try {
        // 通过dynamic调用triggerAddSpotMode方法
        (mapPageState as dynamic).triggerAddSpotMode();
      } catch (e) {
        debugPrint('调用MapPage方法时出错: $e');
      }
    }
  }

  /// 调用MapPage的添加活动功能
  void _callMapPageAddActivity() {
    final mapPageState = _mapPageKey.currentState;
    if (mapPageState != null) {
      // 通过dynamic调用triggerAddActivityMode方法
      (mapPageState as dynamic).triggerAddActivityMode();
      debugPrint('🔍 [MainScreen] 已调用MapPage的添加活动功能');
    } else {
      debugPrint('🔍 [MainScreen] 无法获取MapPage实例');
    }
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用从后台恢复时，静默尝试刷新登录状态
        _refreshAuthStateOnResume();
        break;
      case AppLifecycleState.paused:
        // 应用切换到后台时的处理
        debugPrint('应用切换到后台');
        break;
      case AppLifecycleState.detached:
        // 应用即将终止时的处理
        debugPrint('应用即将终止');
        break;
      case AppLifecycleState.inactive:
        // 应用失去焦点时的处理
        debugPrint('应用失去焦点');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏时的处理
        debugPrint('应用被隐藏');
        break;
    }
  }

  /// 应用恢复时刷新认证状态
  Future<void> _refreshAuthStateOnResume() async {
    try {
      // 静默尝试刷新登录状态，不显示任何错误提示
      if (!Services.auth.isLoggedIn) {
        await Services.auth.initialize();
      }
    } catch (e) {
      // 静默处理错误，不影响用户体验
      debugPrint('应用恢复时刷新认证状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于AutomaticKeepAliveClientMixin


    // 监听主题变化，确保底部导航栏能实时更新
    return ListenableBuilder(
      listenable: AppThemeManager.instance,
      builder: (context, child) {
        return PopScope(
          canPop: false, // 阻止默认的返回行为
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              // 检查当前是否在地图页面的分屏模式
              if (_currentIndex == 0) {
                final mapPageState = _mapPageKey.currentState;
                if (mapPageState != null) {
                  final isSplitScreenMode = (mapPageState as dynamic).isSplitScreenMode;
                  if (isSplitScreenMode == true) {
                    return; // 让MapPage处理返回键
                  }
                }
              }
              // 按返回键时将应用切换到后台，而不是退出
              _moveAppToBackground();
            }
          },
          child: Scaffold(
            body: IndexedStack(index: _currentIndex, children: _pages),
            bottomNavigationBar:
                _showBottomNavigationBar ? _buildBottomNavigationBar() : null,
          ),
        );
      },
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return CustomBottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: _handleBottomNavTap,
    );
  }

  /// 构建添加选项项
  Widget _buildAddOptionItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            // 图标容器
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon, 
                size: 24, 
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(width: 16),
            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16, 
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // 右箭头
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 处理添加钓点选择（带平滑过渡）
  void _handleAddSpotSelection(BuildContext context) async {
    // 先关闭 Bottom Sheet，等待动画完成
    Navigator.pop(context);
    
    // 等待 Bottom Sheet 关闭动画完成（通常是 300ms）
    await Future.delayed(const Duration(milliseconds: 350));
    
    // 然后触发添加钓点模式
    _triggerAddSpotMode();
  }

  /// 处理添加活动选择（带平滑过渡）
  void _handleAddActivitySelection(BuildContext context) async {
    // 先关闭 Bottom Sheet，等待动画完成
    Navigator.pop(context);
    
    // 等待 Bottom Sheet 关闭动画完成（通常是 300ms）
    await Future.delayed(const Duration(milliseconds: 350));
    
    // 然后触发添加活动模式
    _triggerAddActivityMode();
  }

  /// 将应用切换到后台
  void _moveAppToBackground() {
    SystemNavigator.pop();
  }
}
