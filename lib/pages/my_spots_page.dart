import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../services/service_locator.dart';

/// 我的钓点页面
class MySpotsPage extends StatefulWidget {
  const MySpotsPage({super.key});

  @override
  State<MySpotsPage> createState() => _MySpotsPageState();
}

class _MySpotsPageState extends State<MySpotsPage> {
  List<FishingSpot> _mySpots = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMySpots();
  }

  Future<void> _loadMySpots() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // 获取用户发布的钓点
      final spots = await Services.fishingSpot.getSpots();
      final mySpots = spots.where((spot) => spot.userId == currentUser.id).toList();

      setState(() {
        _mySpots = mySpots;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的钓点'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _mySpots.isEmpty
              ? _buildEmptyState()
              : _buildSpotsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '还没有发布钓点',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '快去发现并分享好钓点吧！',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 跳转到地图页面添加钓点
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A90E2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('添加钓点'),
          ),
        ],
      ),
    );
  }

  Widget _buildSpotsList() {
    return RefreshIndicator(
      onRefresh: _loadMySpots,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _mySpots.length,
        itemBuilder: (context, index) {
          final spot = _mySpots[index];
          return _buildSpotItem(spot);
        },
      ),
    );
  }

  Widget _buildSpotItem(FishingSpot spot) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: () => _viewSpotDetail(spot),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFF4A90E2).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.location_on,
            color: Color(0xFF4A90E2),
            size: 24,
          ),
        ),
        title: Text(
          spot.name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (spot.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                spot.description,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.favorite,
                  size: 16,
                  color: Colors.red.shade400,
                ),
                const SizedBox(width: 4),
                Text(
                  '${spot.likesCount}',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTime(spot.created),
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, spot),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 18),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        contentPadding: const EdgeInsets.all(16),
      ),
    );
  }

  void _viewSpotDetail(FishingSpot spot) {
    // TODO: 跳转到钓点详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('钓点详情页面待实现')),
    );
  }

  void _handleMenuAction(String action, FishingSpot spot) {
    switch (action) {
      case 'edit':
        // TODO: 跳转到编辑钓点页面
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('编辑钓点功能待实现')),
        );
        break;
      case 'delete':
        _showDeleteConfirmDialog(spot);
        break;
    }
  }

  void _showDeleteConfirmDialog(FishingSpot spot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除钓点'),
        content: Text('确定要删除钓点"${spot.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSpot(spot);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSpot(FishingSpot spot) async {
    try {
      // TODO: 实现删除钓点功能
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('删除钓点功能待实现')),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }
}
