import 'package:flutter/material.dart';
import '../services/service_locator.dart';

/// 新回复页面
class NewRepliesPage extends StatefulWidget {
  const NewRepliesPage({super.key});

  @override
  State<NewRepliesPage> createState() => _NewRepliesPageState();
}

class _NewRepliesPageState extends State<NewRepliesPage> {
  List<Map<String, dynamic>> _replies = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReplies();
  }

  Future<void> _loadReplies() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final replies = await Services.userStats.getNewRepliesList();
      setState(() {
        _replies = replies;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('最新回复'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _replies.isEmpty
              ? _buildEmptyState()
              : _buildRepliesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无新回复',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '当有人回复您的钓点或动态时，会在这里显示',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRepliesList() {
    return RefreshIndicator(
      onRefresh: _loadReplies,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _replies.length,
        itemBuilder: (context, index) {
          final reply = _replies[index];
          return _buildReplyItem(reply);
        },
      ),
    );
  }

  Widget _buildReplyItem(Map<String, dynamic> reply) {
    final isSpotComment = reply['type'] == 'spot_comment';
    final title = isSpotComment 
        ? '钓点: ${reply['spot_name']}'
        : '动态: ${reply['post_title']}';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: () => _navigateToReply(reply),
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF4A90E2).withValues(alpha: 0.1),
          child: Icon(
            isSpotComment ? Icons.location_on : Icons.article,
            color: const Color(0xFF4A90E2),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${reply['user_name']} 回复了您',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              reply['content'],
              style: TextStyle(
                color: Colors.grey.shade800,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(reply['created']),
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: const Icon(
          Icons.chevron_right,
          color: Colors.grey,
        ),
        contentPadding: const EdgeInsets.all(16),
      ),
    );
  }

  void _navigateToReply(Map<String, dynamic> reply) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // 标记为已读
    await Services.userStats.markReplyAsRead(reply['id'], reply['type']);

    if (!mounted) return;

    // 根据类型跳转到相应页面
    if (reply['type'] == 'spot_comment') {
      // 跳转到钓点详情页面
      // Navigator.pushNamed(context, '/spot_detail', arguments: reply['spot_id']);
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('跳转到钓点详情页面（待实现）')),
      );
    } else {
      // 跳转到动态详情页面
      // Navigator.pushNamed(context, '/post_detail', arguments: reply['post_id']);
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('跳转到动态详情页面（待实现）')),
      );
    }

    // 刷新列表
    _loadReplies();
  }

  String _formatTime(String timeString) {
    try {
      final time = DateTime.parse(timeString);
      final now = DateTime.now();
      final difference = now.difference(time);

      if (difference.inMinutes < 1) {
        return '刚刚';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}小时前';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}天前';
      } else {
        return '${time.month}月${time.day}日';
      }
    } catch (e) {
      return timeString;
    }
  }
}
