import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user.dart';
import '../services/unified_image_service.dart';
import '../services/service_locator.dart';

/// 账户信息页面
class AccountInfoPage extends StatefulWidget {
  const AccountInfoPage({super.key});

  @override
  State<AccountInfoPage> createState() => _AccountInfoPageState();
}

class _AccountInfoPageState extends State<AccountInfoPage> {
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _currentUser = Services.auth.currentUser;
    } catch (e) {
      debugPrint('加载用户信息失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text('账户信息'),
        backgroundColor: const Color(0xFF4A90E2),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _currentUser == null
              ? _buildNotLoggedInView()
              : _buildAccountInfoView(),
    );
  }

  Widget _buildNotLoggedInView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text('未登录', style: TextStyle(fontSize: 18, color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text(
            '请先登录查看账户信息',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A90E2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfoView() {
    final user = _currentUser!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户头像和基本信息
          _buildUserHeader(user),

          const SizedBox(height: 30),

          // 基本信息
          _buildSectionTitle('基本信息'),
          const SizedBox(height: 16),

          _buildInfoCard([
            _buildInfoItem(
              icon: Icons.account_circle,
              label: '用户名',
              value: user.username,
              canCopy: true,
            ),
            _buildInfoItem(
              icon: Icons.email,
              label: '邮箱',
              value: user.email,
              canCopy: true,
            ),
            _buildInfoItem(
              icon: Icons.phone,
              label: '手机号',
              value: user.phone ?? '未设置',
              canCopy: user.phone != null,
            ),
          ]),

          const SizedBox(height: 24),

          // 账户状态
          _buildSectionTitle('账户状态'),
          const SizedBox(height: 16),

          _buildInfoCard([
            _buildInfoItem(
              icon: Icons.verified_user,
              label: '邮箱验证',
              value: user.verified ? '已验证' : '未验证',
              valueColor: user.verified ? Colors.green : Colors.orange,
            ),
            _buildInfoItem(
              icon: Icons.visibility,
              label: '邮箱可见性',
              value: user.emailVisibility ? '公开' : '私密',
              valueColor: user.emailVisibility ? Colors.blue : Colors.grey,
            ),
            _buildInfoItem(
              icon: Icons.stars,
              label: '积分',
              value: '${user.points}',
              valueColor: const Color(0xFF4A90E2),
            ),
          ]),

          const SizedBox(height: 24),

          // 时间信息
          _buildSectionTitle('时间信息'),
          const SizedBox(height: 16),

          _buildInfoCard([
            _buildInfoItem(
              icon: Icons.calendar_today,
              label: '注册时间',
              value: _formatDateTime(user.created),
            ),
            _buildInfoItem(
              icon: Icons.update,
              label: '最后更新',
              value: _formatDateTime(user.updated),
            ),
            _buildInfoItem(
              icon: Icons.login,
              label: '最后登录',
              value:
                  user.lastLoginAt != null
                      ? _formatDateTime(user.lastLoginAt!)
                      : '未记录',
            ),
          ]),

          const SizedBox(height: 24),

          // 账户ID（技术信息）
          _buildSectionTitle('技术信息'),
          const SizedBox(height: 16),

          _buildInfoCard([
            _buildInfoItem(
              icon: Icons.fingerprint,
              label: '账户ID',
              value: user.id,
              canCopy: true,
              isMonospace: true,
            ),
          ]),

          const SizedBox(height: 50),
        ],
      ),
    );
  }

  Widget _buildUserHeader(User user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4A90E2), Color(0xFF50C878)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: ClipOval(
              child:
                  user.avatarUrl.isNotEmpty
                      ? UnifiedImageService().buildSignedImage(
                        originalUrl: user.avatarUrl,
                        fit: BoxFit.cover,
                        width: 60,
                        height: 60,
                        errorWidget: Container(
                          color: Colors.white.withValues(alpha: 0.2),
                          child: const Icon(
                            Icons.person,
                            size: 30,
                            color: Colors.white,
                          ),
                        ),
                      )
                      : Container(
                        color: Colors.white.withValues(alpha: 0.2),
                        child: const Icon(
                          Icons.person,
                          size: 30,
                          color: Colors.white,
                        ),
                      ),
            ),
          ),

          const SizedBox(width: 16),

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.username,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '@${user.username}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                if (user.bio != null && user.bio!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    user.bio!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF333333),
      ),
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
    bool canCopy = false,
    bool isMonospace = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: const Color(0xFF4A90E2)),
          const SizedBox(width: 12),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Flexible(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? const Color(0xFF333333),
                fontWeight: FontWeight.w400,
                fontFamily: isMonospace ? 'monospace' : null,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (canCopy) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _copyToClipboard(value),
              child: Icon(Icons.copy, size: 16, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已复制: $text'),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFF4A90E2),
      ),
    );
  }
}
