重要：钓点详情页面
重要：一起钓鱼页面
重要：钓鱼动态页面
重要：钓点编辑修改页面
重要：聊天页面待完善
重要：探索页面还无法工作
头像无法显示
图片无法放大缩小
剪贴板里有钓点无法清楚导致每次启动都提示。
程序启动时的黑屏时间太长

SpotAccessCache只在定义文件中出现，没有在任何其他地方被引用或使用

解决后端ip地址硬编码的问题，使用这两个方式获取后端ip：
curl -s "https://doh.pub/dns-query?name=app.19840112.xyz&type=A"
curl -s "https://dns.alidns.com/resolve?name=app.19840112.xyz&type=A"

优化各个页面从后端数据库加载的内容。地图页面的标签，只需要从数据库请求位置坐标，id ,图标，这三个信息。其他信息都不用。

增加一个搜索指示标记，搜索栏搜索到地址后，点击跳转，会显示这个地址标记，这个地址标记只有一个。
