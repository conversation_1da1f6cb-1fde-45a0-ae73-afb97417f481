# 鱼窝子钓鱼社交应用需求文档

## 简介

鱼窝子是一个专为钓鱼爱好者设计的社交平台应用，基于Flutter和PocketBase构建。用户可以分享钓点位置、上传钓鱼照片、与其他钓友交流互动，并发现附近的优质钓点。

## 需求

### 需求1：用户认证与账户管理

**用户故事：** 作为钓鱼爱好者，我希望能够注册账户并登录应用，以便保存我的钓点信息和与其他钓友互动。

#### 验收标准

1. WHEN 用户首次打开应用 THEN 系统应显示启动页面并检查登录状态
2. WHEN 用户未登录 THEN 系统应提供邮箱注册和登录功能
3. WHEN 用户输入有效的邮箱和密码 THEN 系统应成功创建账户或登录
4. WHEN 用户成功登录过一次 THEN 系统应在下次启动时自动登录
5. WHEN 用户登录成功 THEN 系统应跳转到主界面
6. WHEN 用户在个人资料页面点击退出登录 THEN 系统应清除登录状态并返回登录页面


### 需求2：地图浏览与钓点展示

**用户故事：** 作为钓鱼爱好者，我希望在地图上查看附近的钓点，以便找到合适的钓鱼地点。

#### 验收标准

1. WHEN 用户进入主页 THEN 系统应显示交互式地图界面
2. WHEN 地图加载完成 THEN 系统应显示用户当前位置和附近的钓点标记
3. WHEN 用户拖动或缩放地图 THEN 系统应动态加载新区域内的钓点
4. WHEN 用户点击地图控制按钮 THEN 系统应支持切换地图类型（矢量图/卫星图）
5. WHEN 用户点击注记层按钮 THEN 系统应切换地图注记层的显示状态
6. WHEN 用户点击位置重置按钮 THEN 系统应将地图中心移动到用户当前位置
7. WHEN 用户点击钓点标记 THEN 系统应显示钓点详细信息

### 需求3：钓点创建与管理

**用户故事：** 作为钓鱼爱好者，我希望能够添加新的钓点并管理我发布的钓点，以便与其他钓友分享优质钓点。

#### 验收标准

1. WHEN 用户在主页点击添加钓点按钮 THEN 系统应进入分屏添加钓点模式
2. WHEN 用户在分屏模式下移动地图 THEN 系统应实时更新中心标记位置
3. WHEN 用户填写钓点信息表单 THEN 系统应验证必填字段（钓点名称）
4. WHEN 用户选择钓点照片 THEN 系统应支持从相册选择或拍照上传
5. WHEN 用户设置钓点可见性 THEN 系统应支持公开、私有等可见性选项
6. WHEN 用户在钓点附近50米内发布 THEN 系统应标记为"实地发布"并增加可信度
7. WHEN 用户提交钓点信息 THEN 系统应保存到数据库并在地图上显示新钓点
8. WHEN 用户查看自己发布的钓点 THEN 系统应允许编辑或删除操作

### 需求4：钓点搜索与筛选

**用户故事：** 作为钓鱼爱好者，我希望能够搜索和筛选钓点，以便快速找到符合我需求的钓鱼地点。

#### 验收标准

1. WHEN 用户进入搜索页面 THEN 系统应显示搜索框和分类标签
2. WHEN 用户输入搜索关键词 THEN 系统应实时搜索钓点名称、描述和地址
3. WHEN 用户选择"附近"标签 THEN 系统应显示按距离排序的附近钓点
4. WHEN 用户选择"热门"标签 THEN 系统应显示按点赞数排序的热门钓点
5. WHEN 用户滚动到列表底部 THEN 系统应自动加载更多钓点
6. WHEN 用户点击搜索结果中的钓点 THEN 系统应跳转到钓点详情页面
7. WHEN 用户点击钓点位置信息 THEN 系统应在地图上显示该钓点位置

### 需求5：社交互动功能

**用户故事：** 作为钓鱼爱好者，我希望能够与其他钓友互动，包括点赞、评论和关注，以便建立钓友社交网络。

#### 验收标准

1. WHEN 用户查看钓点详情 THEN 系统应显示点赞、评论和收藏按钮
2. WHEN 用户点击点赞按钮 THEN 系统应更新点赞状态和点赞数量
3. WHEN 用户点击评论按钮 THEN 系统应显示评论输入框
4. WHEN 用户提交评论 THEN 系统应保存评论并显示在评论列表中
5. WHEN 用户点击收藏按钮 THEN 系统应将钓点添加到用户收藏列表
6. WHEN 用户查看其他用户资料 THEN 系统应显示关注按钮
7. WHEN 用户点击关注按钮 THEN 系统应建立关注关系

### 需求6：个人资料与统计

**用户故事：** 作为钓鱼爱好者，我希望能够管理个人资料并查看我的钓鱼统计数据，以便展示我的钓鱼经历。

#### 验收标准

1. WHEN 用户进入个人资料页面 THEN 系统应显示用户头像、昵称和基本信息
2. WHEN 用户点击编辑资料 THEN 系统应允许修改昵称、头像和个人简介
3. WHEN 用户点击头像 THEN 系统应提供拍照或从相册选择头像的选项
4. WHEN 用户查看个人统计 THEN 系统应显示发布钓点数、获得点赞数等统计信息
5. WHEN 用户点击"我的钓点" THEN 系统应显示用户发布的所有钓点列表
6. WHEN 用户点击"我的收藏" THEN 系统应显示用户收藏的钓点列表
7. WHEN 用户点击"关注/粉丝" THEN 系统应显示关注和粉丝列表

### 需求7：消息与通知

**用户故事：** 作为钓鱼爱好者，我希望能够接收和发送消息，以便与其他钓友私下交流。

#### 验收标准

1. WHEN 用户进入消息页面 THEN 系统应显示消息列表和未读消息提示
2. WHEN 用户收到新消息 THEN 系统应在消息页面显示未读标记
3. WHEN 用户点击消息对话 THEN 系统应打开聊天界面
4. WHEN 用户在聊天界面输入消息 THEN 系统应支持文字消息发送
5. WHEN 用户发送消息 THEN 系统应实时更新对话界面
6. WHEN 用户收到评论或点赞 THEN 系统应发送相应的通知消息
7. WHEN 用户点击通知消息 THEN 系统应跳转到相关的钓点或内容页面

### 需求8：位置服务与验证

**用户故事：** 作为钓鱼爱好者，我希望应用能够准确获取我的位置信息，并验证钓点发布的真实性。

#### 验收标准

1. WHEN 用户首次使用应用 THEN 系统应请求位置权限
2. WHEN 用户授权位置权限 THEN 系统应获取并显示用户当前位置
3. WHEN 用户位置发生变化 THEN 系统应自动更新位置信息
4. WHEN 用户在钓点50米范围内发布 THEN 系统应标记为"实地验证"
5. WHEN 用户发布钓点时 THEN 系统应记录发布位置用于验证
6. WHEN 系统检测到位置权限被拒绝 THEN 系统应显示权限说明并引导用户开启
7. WHEN 用户在离线状态下 THEN 系统应使用缓存的位置信息

### 需求9：数据缓存与离线功能

**用户故事：** 作为钓鱼爱好者，我希望在网络不稳定的户外环境中也能正常使用应用的基本功能。

#### 验收标准

1. WHEN 用户首次加载钓点数据 THEN 系统应将数据缓存到本地存储
2. WHEN 用户在离线状态下打开应用 THEN 系统应显示缓存的钓点数据
3. WHEN 用户在离线状态下查看钓点详情 THEN 系统应显示缓存的详细信息
4. WHEN 网络恢复连接 THEN 系统应自动同步本地和服务器数据
5. WHEN 用户在离线状态下添加钓点 THEN 系统应保存到本地待网络恢复后上传
6. WHEN 地图瓦片加载失败 THEN 系统应使用缓存的地图瓦片
7. WHEN 缓存数据过期 THEN 系统应在网络可用时自动更新

### 需求10：积分系统与奖励机制

**用户故事：** 作为钓鱼爱好者，我希望通过分享钓点和参与社区活动获得积分奖励，并能使用积分兑换商品或服务。

#### 验收标准

1. WHEN 用户发布新钓点 THEN 系统应奖励相应积分（如10-50分）
2. WHEN 用户的钓点被其他用户点赞 THEN 系统应奖励积分（如1-5分）
3. WHEN 用户进行实地验证发布 THEN 系统应给予额外积分奖励
4. WHEN 用户每日登录 THEN 系统应提供签到积分奖励
5. WHEN 用户邀请新用户注册 THEN 系统应奖励推荐积分
6. WHEN 用户查看个人资料 THEN 系统应显示当前积分余额和积分历史
7. WHEN 用户积分达到特定等级 THEN 系统应解锁相应的用户等级和特权

### 需求11：积分商城与购物功能

**用户故事：** 作为钓鱼爱好者，我希望能够使用积分购买钓具商品或兑换特殊服务，以便获得实际的钓鱼用品。

#### 验收标准

1. WHEN 用户进入积分商城 THEN 系统应显示可兑换的商品列表
2. WHEN 用户浏览商品 THEN 系统应显示商品图片、描述、积分价格和库存信息
3. WHEN 用户积分足够 THEN 系统应允许用户兑换商品
4. WHEN 用户确认兑换 THEN 系统应扣除相应积分并生成兑换订单
5. WHEN 用户查看兑换记录 THEN 系统应显示历史兑换订单和物流状态
6. WHEN 商品库存不足 THEN 系统应显示缺货状态并提供补货通知
7. WHEN 用户兑换实物商品 THEN 系统应收集收货地址信息

### 需求12：探索与内容推荐

**用户故事：** 作为钓鱼爱好者，我希望发现附近的钓友动态和优质钓点推荐，以便扩展我的钓鱼社交圈。

#### 验收标准

1. WHEN 用户进入探索页面 THEN 系统应显示个性化推荐内容
2. WHEN 系统推荐钓点 THEN 应优先推荐用户附近的高质量钓点
3. WHEN 系统推荐用户动态 THEN 应基于用户位置、兴趣和社交关系进行推荐
4. WHEN 用户查看推荐内容 THEN 系统应记录用户行为用于优化推荐算法
5. WHEN 用户对推荐内容进行互动 THEN 系统应更新用户兴趣模型
6. WHEN 用户刷新探索页面 THEN 系统应提供新的推荐内容
7. WHEN 用户屏蔽某类内容 THEN 系统应在后续推荐中避免类似内容

### 需求13：约钓功能与活动组织

**用户故事：** 作为钓鱼爱好者，我希望能够发起约钓活动或参加其他钓友的约钓，以便结识更多钓友并享受集体钓鱼的乐趣。

#### 验收标准

1. WHEN 用户发起约钓活动 THEN 系统应允许设置时间、地点、人数限制等信息
2. WHEN 用户发布约钓信息 THEN 系统应向附近的钓友推送约钓通知
3. WHEN 其他用户查看约钓活动 THEN 系统应显示活动详情和参与按钮
4. WHEN 用户报名参加约钓 THEN 系统应记录参与者信息并通知发起者
5. WHEN 约钓活动人数已满 THEN 系统应停止接受新的报名
6. WHEN 约钓活动时间临近 THEN 系统应向参与者发送提醒通知
7. WHEN 约钓活动结束 THEN 系统应允许参与者分享钓鱼成果和评价活动

### 需求14：动态分享与社区互动

**用户故事：** 作为钓鱼爱好者，我希望能够分享我的钓鱼动态和成果，并与其他钓友进行深度交流。

#### 验收标准

1. WHEN 用户发布钓鱼动态 THEN 系统应支持图片、文字和位置信息
2. WHEN 用户分享钓鱼成果 THEN 系统应允许添加鱼类信息、重量等详细数据
3. WHEN 用户发布动态 THEN 系统应向关注者和附近用户推送动态信息
4. WHEN 其他用户查看动态 THEN 系统应提供点赞、评论和转发功能
5. WHEN 用户的动态获得高互动 THEN 系统应给予额外的积分奖励
6. WHEN 用户查看动态列表 THEN 系统应按时间和相关性排序显示
7. WHEN 用户举报不当动态 THEN 系统应提供举报机制并进行内容审核

### 需求15：应用设置与主题

**用户故事：** 作为钓鱼爱好者，我希望能够个性化应用设置，包括主题和通知偏好。

#### 验收标准

1. WHEN 用户进入设置页面 THEN 系统应显示各种设置选项
2. WHEN 用户切换主题模式 THEN 系统应立即应用新的主题样式
3. WHEN 用户修改通知设置 THEN 系统应保存用户偏好并应用到通知行为
4. WHEN 用户清除缓存 THEN 系统应删除本地缓存数据
5. WHEN 用户修改隐私设置 THEN 系统应更新用户的可见性配置
6. WHEN 用户查看关于页面 THEN 系统应显示应用版本和开发信息
7. WHEN 用户在开发模式下 THEN 系统应显示额外的开发者工具选项