#!/bin/bash

# 更新 spot_photos 集合权限规则的脚本
# 允许未登录用户查看公开钓点的照片

echo "🔧 开始更新 spot_photos 集合权限规则..."

# PocketBase 服务器地址
POCKETBASE_URL="http://117.72.60.131:8090"

# 调用权限更新API
echo "📡 正在调用权限更新API..."
response=$(curl -s -X POST \
  "${POCKETBASE_URL}/api/admin/update-spot-photos-permissions" \
  -H "Content-Type: application/json")

echo "📋 服务器响应:"
echo "$response" | jq '.' 2>/dev/null || echo "$response"

# 检查响应是否成功
if echo "$response" | grep -q '"success":true'; then
    echo "✅ spot_photos 权限规则更新成功！"
    echo "🎉 未登录用户现在可以查看公开钓点的照片了"
else
    echo "❌ spot_photos 权限规则更新失败"
    echo "🔍 请检查服务器日志或手动更新权限规则"
fi

echo ""
echo "📝 权限规则说明:"
echo "   listRule: @request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'"
echo "   viewRule: @request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'"
echo ""
echo "这意味着："
echo "   ✓ 已登录用户可以查看所有照片"
echo "   ✓ 未登录用户只能查看公开钓点的照片"
echo "   ✓ 私有钓点的照片仍然受到保护"