#!/bin/bash

# 创建spot_photos集合的脚本
SERVER_URL="http://117.72.60.131:8090"

echo "🚀 开始创建 spot_photos 集合..."

# 创建 spot_photos 集合
echo "📸 创建 spot_photos 集合..."
RESPONSE=$(curl -X POST "${SERVER_URL}/api/create-spot-photos-collection" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s)

echo "响应: $RESPONSE"

echo ""
echo "✅ spot_photos 集合创建完成！"

# 验证集合是否创建成功
echo ""
echo "🔍 验证集合创建结果..."
echo "检查 spot_photos 集合:"
curl -X GET "${SERVER_URL}/api/simple-check/spot_photos" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""
echo "🎉 脚本执行完成！"