地名搜索V2.0
简介
地名搜索服务V2.0是一类简单的HTTP/HTTPS接口，包括普通搜索、视野内搜索、周边搜索、多边形搜索、行政区域搜索、分类搜索、统计搜索。
使用搜索服务V2.0前您需要申请Key，并在控制台中配置权限。
服务目录
1.1 行政区划区域搜索服务
1.2 视野内搜索服务
1.3 周边搜索服务
1.4 多边形搜索服务
1.5 数据分类搜索服务
1.6 普通搜索服务
1.7 统计搜索服务
2.1 返回信息码
一、搜索请求说明
1.1 行政区划区域搜索服务
1.1.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
specify	指定行政区的国标码（行政区划编码表）严格按照行政区划编码表中的（名称，gb码）	String	必填	下载行政区划编码表。9位国标码，如：北京：156110000或北京。
queryType	服务查询类型参数	String	必填	12：行政区划区域搜索服务。
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息； 取值为2，则返回详细poi信息
1.5.2返回参数说明
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		
provinceCode	省行政区编码	String		
city	所属城市名称	String		
cityCode	市行政区编码	String		
county	所属区县名称	String		
countyCode	区县行政区编码	String		
source	数据信息来源	String	必返回	
typeCode	分类编码	String		
typeName	分类名称	String		
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
statistics	针对统计（类型2）集合返回	Json 数组		resultType=2
count	本次统计POI总数量	Int	必返回	
adminCount	行政区数量	Int	必返回	
priorityCitys	推荐行政区名称	Json数组	必返回	
name	行政区名称	String	必返回	
count	城市数量	Int	必返回	
lonlat	行政区经纬度	String	必返回	坐标 x，y
ename	英文行政名称	String	必返回	
adminCode	城市国标码	Int	必返回	9位国标码。
allAdmins	各省包含信息集合	Json数组	必返回	
name	行政名称	String	必返回	
count	包含数量	Int	必返回	
lonlat	行政区经纬度	String	必返回	坐标x,y
adminCode	省国标码	String	必返回	
ename	英文行政名称	String	必返回	
isleaf	有无下一级行政区	boolean	必返回	有则false，无则true
area	针对行政区省（类型3）集合点	Json 数组		resultType=3
name	名称	String	必返回	
bound	定位范围(“minx,miny,maxx,maxy”)	String	返回	
lonlat	定位中心点坐标	String	必返回	
adminCode	行政区编码	Int	必返回	
level	显示级别	Int	必返回	1-18级
lineData	线路结果	Json 数组		resultType=5
stationNum	站数量	String	必返回	
poiType	类型为“103”	String	必返回	
name	线路名称	String	必返回	
uuid	线路id	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.1.3行政区划区域搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"商厦","queryType":12,"start":0,"count":10,"specify":"156110108"}&type=query&tk=您的密钥
1.2视野内搜索服务
1.2.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
mapBound	地图视野范围(“minx,miny,maxx,maxy”)	String	必填	-180,-90至180,90。
level	目前查询的级别	String	必填	1-18级
queryType	服务查询类型参数	String	必填	2：视野内搜索
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息； 取值为2，则返回详细poi信息
1.2.2返回参数说明
响应的数据格式为Json格式。
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		
provinceCode	省行政区编码	String		
city	所属城市名称	String		
cityCode	市行政区编码	String		
county	所属区县名称	String		
countyCode	区县行政区编码	String		
source	数据信息来源	String	必返回	
typeCode	分类编码	String		
typeName	分类名称	String		
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	提示类型： 1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
keyword	关键字	String	必返回	
statistics	针对统计（类型2）集合返回	Json 数组		resultType=2
count	搜索结果总条数	Int	必返回	
adminCount	行政区数量	Int	必返回	
priorityCitys	推荐行政区名称	Json数组	必返回	
name	行政区名称	String	必返回	
count	城市数量	Int	必返回	
lonlat	行政区中心点经纬度	String	必返回	坐标 x，y
ename	英文行政名称	String	必返回	
adminCode	城市国标码	Int	必返回	9位国标码。
allAdmins	各省包含信息集合	Json数组	必返回	
name	行政名称	String	必返回	
count	包含数量	Int	必返回	
lonlat	行政区中心点经纬度	String	必返回	坐标x,y
adminCode	省国标码	String	必返回	
ename	英文行政名称	String	必返回	
isleaf	有无下一级行政区	boolean	必返回	有则false，无则true
area	针对行政区省（类型3）集合点	Json 数组		resultType=3
name	名称	String	必返回	
bound	定位范围(“minx,miny,maxx,maxy”)	String	返回	
lonlat	定位中心点坐标	String	必返回	
adminCode	行政区编码	Int	必返回	
level	显示级别	Int	必返回	1-18级
lineData	线路结果	Json 数组		resultType=5
stationNum	站数量	String	必返回	
poiType	类型为“103”	String	必返回	poiType=102
name	线路名称	String	必返回	
uuid	线路id	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.2.3视野内搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"医院","level":12,"mapBound":"116.02524,39.83833,116.65592,39.99185","queryType":2,"start":0,"count":10}&type=query&tk=您的密钥
1.3周边搜索服务
1.3.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
queryRadius	查询半径	String	必填	单位:米 （10公里内）
pointLonlat	点坐标	String	必填	中心点，经纬度坐标
queryType	服务查询类型参数	String	必填	3：周边搜索服务。
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息；取值为2，则返回详细poi信息
1.3.2返回参数说明
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		
provinceCode	省行政区编码	String		
city	所属城市名称	String		
cityCode	市行政区编码	String		
county	所属区县名称	String		
countyCode	区县行政区编码	String		
source	数据信息来源	String	必返回	
distance	距离（单位 m,km）	String	必返回	1千米以下单位为米（m），1千米以上单位为千米（km）
typeCode	分类编码	String		
typeName	分类名称	String		
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	提示类型： 1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
keyword	关键字	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.3.3周边搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"公园","level":12,"queryRadius":5000,"pointLonlat":"116.48016,39.93136","queryType":3,"start":0,"count":10}&type=query&tk=您的密钥
1.4 多边形搜索服务
1.4.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
polygon	多边形范围数据(经纬度坐标对)	String	必填	经度和纬度用","分割，首尾坐标对需相同。实例(x1,y1,x2,y2,x3,y3,x4,y4,x5,y5,x1,y1)
queryType	服务查询类型参数	String	必填	10：多边形搜索服务。
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息； 取值为2，则返回详细poi信息
1.4.2返回参数说明
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		新加
provinceCode	省行政区编码	String		新加
city	所属城市名称	String		新加
cityCode	市行政区编码	String		新加
county	所属区县名称	String		新加
countyCode	区县行政区编码	String		新加
source	数据信息来源	String	必返回	
typeCode	分类编码	String		新加
typeName	分类名称	String		新加
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	提示类型： 1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
keyword	关键字	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.4.3多边形搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"学校","polygon":"118.93232636500011,27.423305726000024,118.93146426300007,27.30976105800005,118.80356153600007,27.311829507000027,118.80469010700006,27.311829508000073,118.8046900920001,27.32381604300008,118.77984777400002,27.32381601800006,118.77984779100007,27.312213007000025,118.76792266100006,27.31240586100006,118.76680145600005,27.429347074000077,118.93232636500011,27.423305726000024","queryType":10,"start":0,"count":10}&type=query&tk=您的密钥
1.5 数据分类搜索服务
1.5.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
specify	指定行政区的国标码（行政区划编码表）严格按照行政区划编码表中的（名称，gb码）	String	必填	下载行政区划编码表。9位国标码，如：北京：156110000。
mapBound	查询的地图范围(“minx,miny,maxx,maxy”)	String	必填	-180,-90至180,90。
queryType	服务查询类型参数	String	必填	13:分类搜索服务。
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息； 取值为2，则返回详细poi信息
1.5.2返回参数说明
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		新加
provinceCode	省行政区编码	String		新加
city	所属城市名称	String		新加
cityCode	市行政区编码	String		新加
county	所属区县名称	String		新加
countyCode	区县行政区编码	String		新加
source	数据信息来源	String	必返回	
typeCode	分类编码	String		新加
typeName	分类名称	String		新加
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	提示类型： 1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
keyword	关键字	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.5.3数据分类搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"queryType":13,"start":0,"count":5,"specify":"156110000","dataTypes":"法院,公园"}&type=query&tk=您的密钥
1.6 普通搜索服务
1.6.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
mapBound	查询的地图范围(“minx,miny,maxx,maxy”)	String	必填	-180,-90至180,90。
level	目前查询的级别	String	必填	1-18级
specify	指定行政区的国标码（行政区划编码表）严格按照行政区划编码表中的（名称，gb码）。如指定的行政区划编码是省以上级别则返回是统计数据（不包括直辖市）	String	可选	下载行政区划编码表。9位国标码，如：北京：156110000或北京。
queryType	搜索类型	String	必填	1:普通搜索（含地铁公交） 7：地名搜索
start	返回结果起始位（用于分页和缓存）默认0	String	必填	0-300，表示返回结果的起始位置。
count	返回的结果数量（用于分页和缓存）	String	必填	1-300，返回结果的条数。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息； 取值为2，则返回详细poi信息
1.6.2返回参数说明
响应的数据格式为Json格式。
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
pois	针对点（类型1）集合返回	Pois Json数组		resultType=1
name	Poi点名称	String	必返回	
phone	电话	String		
address	地址	String		
lonlat	坐标	String	必返回	坐标 x，y
poiType	poi类型	Int	必返回	101:POI数据 102:公交站点
eaddress	英文地址	String		
ename	poi点英文名称	String		
hotPointID	poi热点ID	String	必返回	热点id
province	所属省名称	String		
provinceCode	省行政区编码	String		
city	所属城市名称	String		
cityCode	市行政区编码	String		
county	所属区县名称	String		
countyCode	区县行政区编码	String		
source	数据信息来源	String	必返回	
typeCode	分类编码	String		
typeName	分类名称	String		
stationData	车站信息结构体 数据	Json 数组		poiType=102
lineName	线路名称	String	必返回	
uuid	线路的id	String	必返回	
stationUuid	公交站uuid	String	必返回	
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
statistics	针对统计（类型2）集合返回	Json 数组		resultType=2
count	返回搜索POI总数量	Int	必返回	
adminCount	行政区数量	Int	必返回	
priorityCitys	推荐行政区名称	Json数组	必返回	
name	行政区名称	String	必返回	
count	城市数量	Int	必返回	
lonlat	行政区中心点经纬度	String	必返回	坐标 x，y
ename	英文行政名称	String	必返回	
adminCode	城市国标码	Int	必返回	9位国标码。
allAdmins	各省包含信息集合	Json数组	必返回	
name	行政区名称	String	必返回	
count	包含数量	Int	必返回	
lonlat	行政区中心点经纬度	String	必返回	坐标x,y
adminCode	省国标码	String	必返回	
ename	英文行政名称	String	必返回	
isleaf	有无下一级行政区	boolean	必返回	有则false，无则true
area	针对行政区省（类型3）集合点	Json 数组		resultType=3
name	名称	String	必返回	
bound	定位范围(“minx,miny,maxx,maxy”)	String	必返回	
lonlat	定位中心点坐标	String	必返回	
adminCode	行政区编码	Int	必返回	
level	显示级别	Int	必返回	1-18级
lineData	线路结果	Json 数组		resultType=5
stationNum	站数量	String	必返回	
poiType	类型为“103”	String	必返回	poiType=102
name	线路名称	String	必返回	
uuid	线路id	String	必返回	
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.6.3普通搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"北京大学","level":12,"mapBound":"116.02524,39.83833,116.65592,39.99185","queryType":1,"start":0,"count":10}&type=query&tk=您的密钥
1.7 统计搜索服务
1.7.1输入参数说明
参数值	参数说明	参数类型	是否必备	备注（值域）
keyWord	搜索的关键字	String	必填	无
specify	指定行政区的国标码（行政区划编码表）严格按照行政区划编码表中的（名称，gb码）	String	必填	下载行政区划编码表。9位国标码，如：北京：156110000。
queryType	服务查询类型参数	String	必填	14：统计搜索服务。
dataTypes	数据分类（分类编码表）	String	可选	下载分类编码表，参数可以分类名称或分类编码。多个分类用","隔开(英文逗号)。
show	返回poi结果信息类别	String	可选	取值为1，则返回基本poi信息；取值为2，则返回详细poi信息
1.7.2返回参数说明
参数值	参数说明	参数类型	返回条件	备注（值域）
resultType	返回结果类型	Int	必返回	取值1-5，对应不同的响应类型： 1（普通POI），2（统计），3（行政区)，4（建议词搜索），5（线路结果）
count	返回总条数	Int	必返回	
keyword	搜索关键词	String	必返回	搜索的关键字。
prompt	提示信息，如“您是否在XXX，搜索XXX”，其中包含提示文字及对应的行政区码	Json	需要提示时返回	
type	提示类型	Int	必返回	提示类型： 1：是否在where搜what，2：在where无搜索what的结果，3：多个可跳转的行政区提示，4：城市
admins		Json数组	必返回	
adminName	行政区名称	String	必返回	
adminCode	行政区划编码	String	必返回	
keyword	关键字	String	必返回	
statistics	针对统计（类型2）集合返回	Json 数组		resultType=2
count	本次统计POI总数量	Int	必返回	
adminCount	行政区数量	Int	必返回	
priorityCitys	推荐行政区名称	Json数组	必返回	
name	行政区名称	String	必返回	
count	城市数量	Int	必返回	
lonlat	行政区经纬度	String	必返回	坐标 x，y
ename	英文行政名称	String	必返回	
adminCode	城市国标码	Int	必返回	9位国标码。
allAdmins	各省包含信息集合	Json数组	必返回	
name	行政名称	String	必返回	
count	包含数量	Int	必返回	
lonlat	行政区经纬度	String	必返回	坐标x,y
adminCode	省国标码	String	必返回	
ename	英文行政名称	String	必返回	
isleaf	有无下一级行政区	boolean	必返回	有则false，无则true
status	返回状态信息	Json 数组	必返回	结果提示信息
infocode	信息码	Int	必返回	服务状态码表
cndesc	返回中文描述	String	必返回	服务状态码表
1.7.3统计搜索示例
请求实例
http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"学校","queryType":14,"specify":"156110108"}&type=query&tk=您的密钥
2.1 返回信息码
返回码	英文描述	定义	常见原因
1000	OK	服务正常	服务请求正常
2001	Parameter Invalid	请求参数错误	请求参数拼写错误
2002	Parameter formal error	请求参数格式错误	请求参数不符合Json标准
2003	Parameter missing	缺少必填参数	缺少接口中要求的必填参数
2004	Parameter wrong value	枚举值错误	参数枚举值错误
2005	Parameter wrong latlon	经纬度数据错误	传入的经纬度数据错误
2006	Parameter latlon slop over	经纬度越界	传入的经纬度数据量超过20个点
2007	Parameter count slop over	请求数据量溢出	传入的start和count数据问题 或 总数据条数超过500条!
3000	Server error	服务器出错	后台服务器异常出错
3001	No data found	没有找到数据	未找到任何结果
prompt提示示例
例：当Type=1时，会给出一个 admin 一个keyword 还有admincode，此时一般的提示为“是否在XXX搜索名称含XXX的结果”
当Type = 2时，会给出一个 admin 一个keyword 还有admincode， 此时一般提示为“在XXX没有搜索到相关的结果”
当Type = 2时，会给出一个 admin 一个keyword 还有admincode， 此时一般提示为“在XXX没有搜索到相关的结果”
“您是否要找：
广东省汕尾市城区
山西省晋市城区
山西省大同市城区
山西省长治市城区
山西省阳泉市城区
只列出名称即可，点击后，直接用这些关键字搜索即可完成行政区跳转。