# Flutter主题颜色使用指南

## Material Design 3 颜色角色

### 主要颜色 (Primary Colors)
- `primary` - 主要品牌色，用于重要按钮、链接等
- `onPrimary` - 主色上的文字/图标颜色
- `primaryContainer` - 主色容器背景
- `onPrimaryContainer` - 主色容器上的文字颜色

### 次要颜色 (Secondary Colors)  
- `secondary` - 次要强调色
- `onSecondary` - 次要色上的文字颜色
- `secondaryContainer` - 次要色容器
- `onSecondaryContainer` - 次要色容器上的文字

### 表面颜色 (Surface Colors)
- `surface` - 卡片、对话框等表面背景
- `onSurface` - 表面上的主要文字
- `onSurfaceVariant` - 表面上的次要文字（提示文字等）
- `surfaceContainerHighest` - 最高层级表面（输入框背景等）

### 背景颜色 (Background Colors)
- `background` - 页面主背景
- `onBackground` - 背景上的文字

### 边框和分割线
- `outline` - 边框线颜色
- `outlineVariant` - 次要边框颜色

### 错误颜色
- `error` - 错误状态颜色
- `onError` - 错误色上的文字

## 常见组件颜色使用

### 搜索栏
```dart
Container(
  decoration: BoxDecoration(
    color: Theme.of(context).colorScheme.surfaceContainerHighest,
    borderRadius: BorderRadius.circular(10),
  ),
  child: TextField(
    decoration: InputDecoration(
      hintStyle: TextStyle(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      // 使用主题的输入框装饰
      border: InputBorder.none,
    ),
  ),
)
```

### 卡片
```dart
Card(
  color: Theme.of(context).colorScheme.surface,
  child: Text(
    '内容',
    style: TextStyle(
      color: Theme.of(context).colorScheme.onSurface,
    ),
  ),
)
```

### 按钮
```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: Theme.of(context).colorScheme.primary,
    foregroundColor: Theme.of(context).colorScheme.onPrimary,
  ),
  onPressed: () {},
  child: Text('按钮'),
)
```

## 替换硬编码颜色的对照表

| 硬编码颜色 | 推荐的主题颜色 | 用途 |
|-----------|---------------|------|
| `Colors.white` | `colorScheme.surface` | 卡片、对话框背景 |
| `Colors.black` | `colorScheme.onSurface` | 主要文字 |
| `Colors.grey[400]` | `colorScheme.onSurfaceVariant` | 提示文字、次要文字 |
| `Colors.grey[200]` | `colorScheme.surfaceContainerHighest` | 输入框背景 |
| `Colors.blue` | `colorScheme.primary` | 主要按钮、链接 |
| `Colors.red` | `colorScheme.error` | 错误状态 |

## 检查主题适配的方法

1. 在设备上切换系统深色/浅色模式
2. 在代码中强制设置主题模式测试
3. 确保所有文字在不同背景下都清晰可见