创建时间	AccessKey/SecretKey	状态	操作
2025-08-04	
EJ9J_rtFb92tzSSo9m4uhmMQGjWtJhR0mzOUX3aZ
fLImrzPP0_wuwjd3k-YUpxnhqW-Vix6wtQ27gkfv
t0g3s60p6.hd-bkt.clouddn.com


上传策略
最近更新时间: 2024-11-20 16:01:33

上传策略是资源上传时附带的一组配置设定。通过这组配置信息，七牛云存储可以了解用户上传的需求：它将上传什么资源，上传到哪个空间，上传结果是回调通知还是使用重定向跳转，是否需要设置反馈信息的内容，以及授权上传的截止时间等等。

上传策略同时还参与请求验证，可以验证用户对某个资源的上传请求是否完整。



格式
{
    "scope":                          "<Bucket                   string>",
    "isPrefixalScope":                <IsPrefixalScope          int>,
    "deadline":                       <UnixTimestamp            uint32>,
    "insertOnly":                     <AllowFileUpdating        int>,
    "endUser":                        "<EndUserId                string>",
    "returnUrl":                      "<RedirectURL              string>",
    "returnBody":                     "<ResponseBodyForAppClient string>",
    "callbackUrl":                    "<RequestUrlForAppServer   string>",
    "callbackHost":                   "<RequestHostForAppServer  string>",
    "callbackBody":                   "<RequestBodyForAppServer  string>",
    "callbackBodyType":               "<RequestBodyTypeForAppServer  string>",
    "persistentType"：                "<persistentType           string>",
    "persistentOps":                  "<persistentOpsCmds        string>",
    "persistentWorkflowTemplateID":   "<persistentWorkflowTemplateID        string>",
    "persistentNotifyUrl":            "<persistentNotifyUrl      string>",
    "persistentPipeline":             "<persistentPipeline       string>",
    "forceSaveKey":                    <forceSaveKey                bool>,
    "saveKey":                        "<SaveKey                  string>",
    "fsizeMin":                        <FileSizeMin              int64>,
    "fsizeLimit":                      <FileSizeLimit            int64>,
    "detectMime":                      <AutoDetectMimeType       int>,
    "mimeLimit":                      "<MimeLimit                string>",
    "fileType":                       <fileType                  int>,
    "trafficLimit":                   <trafficLimit            int64>
}
字段名	必填	说明
scope	是	指定上传的目标资源空间 Bucket 和资源键 Key（最大为 750 字节）。有三种格式：
<bucket>，表示允许用户上传文件到指定的 bucket。在这种格式下文件只能新增（分片上传 v1 版 需要指定 insertOnly 为 1 才是新增，否则也为覆盖上传），若已存在同名资源（且文件内容/etag不一致），上传会失败；若已存在资源的内容/etag一致，则上传会返回成功。
<bucket>:<key>，表示只允许用户上传指定 key 的文件。在这种格式下文件默认允许修改，若已存在同名资源则会被覆盖。如果只希望上传指定 key 的文件，并且不允许修改，那么可以将下面的 insertOnly 属性值设为 1。
<bucket>:<keyPrefix>，表示只允许用户上传指定以 keyPrefix 为前缀的文件，当且仅当 isPrefixalScope 字段为 1 时生效，isPrefixalScope 为 1 时无法覆盖上传。
isPrefixalScope		若为 1，表示允许用户上传以 scope 的 keyPrefix 为前缀的文件。
deadline	是	上传凭证有效截止时间。Unix时间戳，单位为秒。该截止时间为上传完成后，在七牛空间生成文件的校验时间，而非上传的开始时间，一般建议设置为上传开始时间 + 3600s，用户可根据具体的业务场景对凭证截止时间进行调整。
insertOnly		限定为新增语意。如果设置为非 0 值，则无论 scope 设置为什么形式，仅能以新增模式上传文件。
endUser		唯一属主标识。特殊场景下非常有用，例如根据 App-Client 标识给图片或视频打水印。
returnUrl		Web 端文件上传成功后，浏览器执行 303 跳转的 URL。通常用于表单上传。文件上传成功后会跳转到 <returnUrl>?upload_ret=<queryString>，<queryString>包含 returnBody 内容。如不设置 returnUrl，则直接将 returnBody 的内容返回给客户端。
returnBody		上传成功后，自定义七牛云最终返回給上传端（在指定 returnUrl 时是携带在跳转路径参数中）的数据。支持魔法变量和自定义变量。returnBody 要求是合法的 JSON 文本。例如 {“key”: $(key), “hash”: $(etag), “w”: $(imageInfo.width), “h”: $(imageInfo.height)}。
callbackUrl		上传成功后，七牛云向业务服务器发送 POST 请求的 URL。必须是公网上可以正常进行 POST 请求并能成功响应的有效 URL。另外，为了给客户端有一致的体验，我们要求 callbackUrl 返回包 Content-Type 为 “application/json”，即返回的内容必须是合法的 JSON 文本。出于高可用的考虑，本字段允许设置多个 callbackUrl（用英文符号 ; 分隔），在前一个 callbackUrl 请求失败的时候会依次重试下一个 callbackUrl。一个典型例子是：http://<ip1>/callback;http://<ip2>/callback，并同时指定下面的 callbackHost 字段。在 callbackUrl 中使用 ip 的好处是减少对 dns 解析的依赖，可改善回调的性能和稳定性。指定 callbackUrl，必须指定 callbackbody，且值不能为空。
callbackHost		上传成功后，七牛云向业务服务器发送回调通知时的 Host 值。与 callbackUrl 配合使用，仅当设置了 callbackUrl 时才有效。
callbackBody		上传成功后，七牛云向业务服务器发送 Content-Type: application/x-www-form-urlencoded 的 POST 请求。业务服务器可以通过直接读取请求的 query 来获得该字段，支持魔法变量和自定义变量。callbackBody 要求是合法的 url query string。例如key=$(key)&hash=$(etag)&w=$(imageInfo.width)&h=$(imageInfo.height)。如果callbackBodyType指定为application/json，则callbackBody应为json格式，例如:{“key”:"$(key)",“hash”:"$(etag)",“w”:"$(imageInfo.width)",“h”:"$(imageInfo.height)"}。
callbackBodyType		上传成功后，七牛云向业务服务器发送回调通知 callbackBody 的 Content-Type。默认为 application/x-www-form-urlencoded，也可设置为 application/json。
persistentType		资源上传成功后触发执行预转持久化处理的任务类型。
- 0 为普通任务，1 为闲时任务
- 对于闲时任务的功能介绍、使用场景、定价，详见闲时任务策略说明。
- 当前只有部分多媒体处理支持设置闲时任务，如下:
1. 普通转码（GPU 转码不支持）
2. 锐智转码2.0（1.0 不支持闲时，如需使用请升级到 2.0）
3. 音视频拼接
4. 音视频分段
5. 视频截图：视频单帧缩略图、视频采样缩略图、视频雪碧截图
persistentOps		资源上传成功后触发执行的持久化处理指令列表。
每个指令是一个 API 规格字符串以;分隔，可以指定多个数据处理命令,如：avthumb/mp4|saveas/cWJ1Y2tldDpxa2V5;avthumb/flv|saveas/cWJ1Y2tldDpxa2V5Mg==，是将上传的视频文件同时转码成mp4格式和flv格式后另存。
fileType=2或3（上传归档存储或深度归档存储文件）时，不支持使用该参数。支持魔法变量和自定义变量。
注意： persistentOps 和 persistentWorkflowTemplateID 两个参数只能二选一。
persistentWorkflowTemplateID		资源上传成功后指定工作流模板即可执行媒体处理操作。
工作流模板是预先编排好的一系列媒体处理流程（如转码、截图、视频拼接等各类处理），登录 对象存储控制台 ，选择具体空间后，进行创建，详情参考工作流模板操作指南，persistentWorkflowTemplateID 对应工作流模板列表的名称字段，只支持 V1 工作流版本。
注意： persistentWorkflowTemplateID 和 persistentOps 两个参数只能二选一。
persistentNotifyUrl		接收持久化处理结果通知的 URL。必须是公网上可以正常进行 POST 请求并能成功响应的有效 URL。该 URL 获取的内容和持久化处理状态查询的处理结果一致。发送 body 格式是 Content-Type 为 application/json 的 POST 请求，需要按照读取流的形式读取请求的 body 才能获取。
persistentPipeline		转码队列名。仅适用于【普通任务】指定，【闲时任务】不能指定队列。
资源上传成功后，触发转码时指定独立的队列进行转码。为空则表示使用公用队列，处理速度比较慢。建议使用专用队列。
forceSaveKey		saveKey的优先级设置。为 true 时，saveKey不能为空，会忽略客户端指定的key，强制使用saveKey进行文件命名。参数不设置时，默认值为false
saveKey		自定义资源名。支持魔法变量和自定义变量。forceSaveKey 为false时，这个字段仅当用户上传的时候没有主动指定 key 时起作用；forceSaveKey 为true时，将强制按这个字段的格式命名。
fsizeMin		限定上传文件大小最小值，单位Byte。小于限制上传文件大小的最小值会被判为上传失败，返回 400 状态码。
fsizeLimit		限定上传文件大小最大值，单位Byte。超过限制上传文件大小的最大值会被判为上传失败，返回 413 状态码。
detectMime		开启 MimeType 侦测功能，并按照下述规则进行侦测；如不能侦测出正确的值，会默认使用 application/octet-stream 。
设为 1 值，则忽略上传端传递的文件 MimeType 信息，并按如下顺序侦测 MimeType 值：
1. 侦测内容以识别；
2. 检查文件扩展名以修正；
3. 检查 Key 扩展名以修正。
默认设为 0 值，如上传端指定了 MimeType（application/octet-stream 除外）则直接使用该值，否则按如下顺序侦测 MimeType 值：
1. 检查文件扩展名以识别；
2. 检查 Key 扩展名以识别；
3. 侦测内容以识别。
设为 -1 值，无论上传端指定了何值直接使用该值。
mimeLimit		限定用户上传的文件类型。无论 detectMime 是否定义，当指定本字段 mimeLimit 值后，七牛服务器会侦测文件内容以判断 MimeType，再用判断值跟指定值进行匹配，匹配成功则允许上传，匹配失败则返回 403 状态码。示例：
image/* 表示只允许上传图片类型
image/jpeg;image/png 表示只允许上传 jpg 和 png 类型的图片
!application/json;text/plain 表示禁止上传 json 文本和纯文本。注意最前面的感叹号！
fileType		文件存储类型。0 为标准存储（默认），1 为低频存储，2 为归档存储，3 为深度归档存储，4 为归档直读存储，5 为智能分层存储
trafficLimit		上传请求单链接速度限制，控制客户端带宽占用。限速值取值范围为 819200 ~ 838860800，单位为bit/s。
使用说明：

Key 必须采用 utf-8 编码，使用非 utf-8 编码的资源名访问时会报错。
callbackUrl 与 callbackBody 配合使用，returnUrl 与 returnBody 配合使用，callbackXXX 与 returnXXX 不可混用。当同时设置 returnUrl 和 callbackUrl 字段时，优先启用 callbackUrl 回调并返回 callbackBody，更多详情请见自定义响应。
文件上传后的命名将遵循以下规则：
源 Bucket 和目标 Bucket 必须在同一区域，即处理结果不能跨区域另存。
forceSaveKey=false，以客户端指定的 Key 为高优先级命名
客户端已指定 Key，以 Key 命名
客户端未指定 Key，上传策略中设置了 saveKey，以 saveKey 的格式命名。
客户端未指定 Key，上传策略中未设置 saveKey，以文件 hash(etag) 命名。
forceSaveKey=true，以上传策略中的 saveKey 为高优先级命名；此时上传策略中的 saveKey 不允许为空
客户端已指定 Key，以上传策略中 saveKey 的格式命名
客户端未指定 Key，以上传策略中 saveKey 的格式命名
文件分片上传的创建文件步骤中。若未指定 Key，为达到不覆盖同名资源效果，必须使用 insertOnly 字段。


persistentOps 详解
persistentOps 字段用于指定预转数据处理命令和保存处理结果的存储空间与资源名。在上传归档存储或深度归档存储文件（fileType=2或3）时，不支持使用该字段。

为此字段指定非空值，则在成功上传一个文件后，会启动一个异步数据处理任务。persistentId 字段，唯一标识此任务。
当 returnBody 中指定了 persistentId 魔法变量时，客户端收到的响应内容 returnBody 中会有 persistentId；当没有指定 returnBody 时，默认也会返回 persistentId。

使用默认的存储空间和资源名

当只指定了数据处理命令时，服务端会选择上传文件的 Bucket 作为数据处理结果的存储空间，Key 由七牛服务器自动生成。

使用指定的存储空间和资源名

在数据处理命令后用管道符|拼接saveas/<encodedEntryURI>指令，指示七牛服务器使用EncodedEntryURI格式中指定的 Bucket 与 Key 来保存处理结果（需要注意的是，如果指定的 Bucket 中存在同 Key 的文件将会被处理结果覆盖）。如 avthumb/flv|saveas/cWJ1Y2tldDpxa2V5，是将上传的视频文件转码 flv 格式后存储为qbucket:qkey，其中cWJ1Y2tldDpxa2V5是qbucket:qkey的URL安全的Base64编码结果。以上方式可以同时作用于多个数据处理命令，用;分隔，如 avthumb/mp4|saveas/cWJ1Y2tldDpxa2V5;avthumb/flv|saveas/cWJ1Y2tldDpxa2V5Mg==



示例
persistentOps与persistentNotifyUrl字段

上传一个视频资源，并在成功后触发两个预转处理（转成 mp4 资源和对原资源进行 HLS 切片）：

{
    "scope":                "qiniu-ts-demo",
    "deadline":             1390528576,
    "persistentOps":        "avthumb/mp4;avthumb/m3u8/noDomain/1/segtime/15/vb/440k",
    "persistentNotifyUrl":  "http://fake.com/qiniu/notify"
}
关于 avthumb 接口的详细信息请参阅音视频转码。


上传凭证
最近更新时间: 2022-11-10 17:23:16

客户端上传前需要先从服务端获取上传凭证，并在上传资源时将上传凭证作为请求内容的一部分。不带凭证或带非法凭证的请求将返回 HTTP 错误码 401，代表认证失败。

生成上传凭证时需要指定以下要素：

权限。指定上传的目标空间或允许覆盖的指定资源。
凭证有效期。即一个符合Unix时间戳规范的数值，单位为秒。
注意：因为 Unix 时间戳的创建和验证在不同的服务端进行（在业务服务器创建，在云存储服务器验证），因此开发者的业务服务器需要尽可能校准时间，否则可能出现凭证刚创建就过期等问题。
可选择设置的最终用户标识 ID。这是为了让业务服务器在收到结果回调时能够识别产生该请求的最终用户信息。
我们使用上传策略来保存和传递这些设置。上传凭证是七牛云存储用于验证上传请求合法性的机制。用户通过上传凭证授权客户端，使其具备访问指定资源的能力。



算法
1.构造上传策略：

用户根据业务需求，确定上传策略要素，构造出具体的上传策略。例如用户要向空间 my-bucket 上传一个名为 sunflower.jpg 的图片，授权有效期截止到 2015-12-31 00:00:00（该有效期指上传完成后在七牛生成文件的时间，而非上传的开始时间），并且希望得到图片的名称、大小、宽高和校验值。那么相应的上传策略各字段分别为：

scope = 'my-bucket:sunflower.jpg'
deadline = 1451491200
returnBody = '{
      "name": $(fname),
      "size": $(fsize),
      "w": $(imageInfo.width),
      "h": $(imageInfo.height),
      "hash": $(etag)
}'
2.将上传策略序列化成为JSON：

用户可以使用各种语言的 JSON 库，也可以手工拼接字符串。序列化后，应得到如下形式的字符串（字符串值以外部分不含空格或换行）：

putPolicy = 
'{
  "scope": "my-bucket:sunflower.jpg",
  "deadline":1451491200,
  "returnBody":
  "{
    \"name\":$(fname),
    \"size\":$(fsize),
    \"w\":$(imageInfo.width),
    \"h\":$(imageInfo.height),
    \"hash\":$(etag)
   }"
}'
3.对 JSON 编码的上传策略进行URL 安全的 Base64 编码，得到待签名字符串：

encodedPutPolicy = urlsafe_base64_encode(putPolicy)
#实际值为：
encodedPutPolicy = "eyJzY29wZSI6Im15LWJ1Y2tldDpzdW5mbG93ZXIuanBnIiwiZGVhZGxpbmUiOjE0NTE0OTEyMDAsInJldHVybkJvZHkiOiJ7XCJuYW1lXCI6JChmbmFtZSksXCJzaXplXCI6JChmc2l6ZSksXCJ3XCI6JChpbWFnZUluZm8ud2lkdGgpLFwiaFwiOiQoaW1hZ2VJbmZvLmhlaWdodCksXCJoYXNoXCI6JChldGFnKX0ifQ=="
4.使用访问密钥（AK/SK）对上一步生成的待签名字符串计算HMAC-SHA1签名：

sign = hmac_sha1(encodedPutPolicy, "<SecretKey>")
#假设 SecretKey 为 MY_SECRET_KEY，实际签名为：
sign = "c10e287f2b1e7f547b20a9ebce2aada26ab20ef2"
注意：签名结果是二进制数据，此处输出的是每个字节的十六进制表示，以便核对检查。

5.对签名进行URL安全的Base64编码：

encodedSign = urlsafe_base64_encode(sign)
#最终签名值为：
encodedSign = "wQ4ofysef1R7IKnrziqtomqyDvI="
6.将访问密钥（AK/SK）、encodedSign 和 encodedPutPolicy 用英文符号 : 连接起来：

uploadToken = AccessKey + ':' + encodedSign + ':' + encodedPutPolicy
#假设用户的 AccessKey 为 MY_ACCESS_KEY ，则最后得到的上传凭证应为：
uploadToken = "MY_ACCESS_KEY:wQ4ofysef1R7IKnrziqtomqyDvI=:eyJzY29wZSI6Im15LWJ1Y2tldDpzdW5mbG93ZXIuanBnIiwiZGVhZGxpbmUiOjE0NTE0OTEyMDAsInJldHVybkJvZHkiOiJ7XCJuYW1lXCI6JChmbmFtZSksXCJzaXplXCI6JChmc2l6ZSksXCJ3XCI6JChpbWFnZUluZm8ud2lkdGgpLFwiaFwiOiQoaW1hZ2VJbmZvLmhlaWdodCksXCJoYXNoXCI6JChldGFnKX0ifQ=="
注意：为确保客户端、业务服务器和七牛服务器对于授权截止时间的理解保持一致，需要同步校准各自的时钟。频繁返回 401 状态码时请先检查时钟同步性与生成 deadline 值的代码逻辑。


下载凭证
最近更新时间: 2021-07-14 19:47:26

下载凭证是用于对私有资源下载的授权，通过授权客户端，使其具备访问指定私有资源的能力。下载私有资源的请求需要带一个合法的下载凭证。不带凭证或带非法凭证的请求将返回 HTTP 错误码 401，代表认证失败。下载公开空间资源不需要下载凭证。


算法
1.构造下载 URL：

DownloadUrl = 'http://78re52.com1.z0.glb.clouddn.com/resource/flower.jpg'
2.为下载 URL 加上过期时间 e 参数，Unix时间戳：

DownloadUrl = 'http://78re52.com1.z0.glb.clouddn.com/resource/flower.jpg?e=1451491200'
3.对上一步得到的 URL 字符串计算HMAC-SHA1签名（假设访问密钥（AK/SK）是 MY_SECRET_KEY），并对结果做URL 安全的 Base64 编码：

Sign = hmac_sha1(DownloadUrl, 'MY_SECRET_KEY')
EncodedSign = urlsafe_base64_encode(Sign)
4.将访问密钥（AK/SK）（假设是 MY_ACCESS_KEY）与上一步计算得到的结果用英文符号 : 连接起来：

Token = 'MY_ACCESS_KEY:438dd8pXocjYuF-6dTcKMtETB2g='
5.将上述 Token 拼接到含过期时间参数 e 的 DownloadUrl 之后，作为最后的下载 URL：

RealDownloadUrl = 'http://78re52.com1.z0.glb.clouddn.com/resource/flower.jpg?e=1451491200&token=MY_ACCESS_KEY:438dd8pXocjYuF-6dTcKMtETB2g='
RealDownloadUrl 即为下载对应私有资源的可用 URL (带有参数的情况下,需要将query一起进行签名)，并在指定时间后失效。失效后，可按需要重新生成下载凭证。

注意：

为确保客户端、业务服务器和七牛服务器对于授权截止时间的理解保持一致，需要同步校准各自的时钟。频繁返回 401 状态码时请先检查时钟同步性与生成 e 参数值的代码逻辑。
token 必须放在请求的最后，token 之后的参数会被忽略。以请求 http://test.cinem.net/aaaa.jpg?e=1778754963&token=sQvk4AXf0rEkzcytkr...XjI0M:zwvwiM0wsMBRj46xcby05U=&attname=geral_TS-PFS3010-8ET为例,此时attname=geral_TS-PFS3010-8ET会被忽略，并不生效。
对于包含中文或其它非英文字符的 Key，需要做到以下两点：
Key 本身要做 UTF-8 编码
为 URL 签名之前，对 Path 部分（不含前导 / 符号，通常就是 Key 本身，即上例中的 resource/flower.jpg）做一次URL 安全的 Base64 编码。

